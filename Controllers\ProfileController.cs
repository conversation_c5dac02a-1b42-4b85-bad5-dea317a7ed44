using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using idigix.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using idigix.Services;
using idigix.Services.interfaces;

namespace idigix.Controllers;
[Route("api/[controller]")]
[ApiController]
public class ProfileController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly dbContext _cnx;
    private readonly ICouponService _couponService;
    private readonly IEmailSender _emailSender;
    private readonly IEncryptionService _encryptionService;
    private readonly ITokenService _tokenService;

    public ProfileController(IConfiguration configuration, dbContext cnx, ICouponService couponService, IEmailSender emailSender, IEncryptionService encryptionService, ITokenService tokenService)
    {
        _configuration = configuration;
        _cnx = cnx;
        _couponService = couponService;
        _emailSender = emailSender;
        _encryptionService = encryptionService;
        _tokenService = tokenService;
    }

    [HttpGet("GetProfile")]
    [Authorize]
    public async Task<IActionResult> GetProfile()
    {
        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        if (userId == 0)
            return BadRequest("userId is null");

        var options = new JsonSerializerOptions
        {
            ReferenceHandler = ReferenceHandler.Preserve,
        };

        var user = await _cnx.Users.Include(p => p.Auth)
        .Include(p => p.Profile).Include(p => p.Contacts).Include(p => p.Ratings).FirstOrDefaultAsync(p => p.Id == userId);

        var jsonString = JsonSerializer.Serialize(user, options);
        return Ok(user);
    }

    [HttpGet("GetProfilesFromLink/{username}")]
    public async Task<IActionResult> GetProfilesFromLink(string? username)
    {
        var user = await _cnx.Users.Include(p => p.Profile)
            .Include(p => p.Profile.SocialLinks)
            .Include(p => p.Profile.CustomLinks)
            .Include(p => p.Contacts)
            .FirstOrDefaultAsync(w => w.Profile.UserName == username);

        if (user == null)
            return StatusCode(404, "user not founds");

        // if (user.Category == "Premium/Enterprise" || user.Category == "Premium/Freelance")
        // {
        //     await _couponService.CalculateGlobalRate(user);
        //     await _cnx.SaveChangesAsync();
        // }

        var options = new JsonSerializerOptions
        {
            ReferenceHandler = ReferenceHandler.Preserve,
        };
        var jsonString = JsonSerializer.Serialize(user, options);

        return Ok(user);
    }

    [HttpPut("EditProfile")]
    [Authorize]
    public async Task<IActionResult> EditProfile([FromBody] postUser newProfile)
    {
        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        var profile = await _cnx.Profile
            .Include(w => w.User)
            .FirstOrDefaultAsync(w => w.UserId == userId);

        if (profile is null)
            return NotFound();

        // If username is being changed, validate it
        if (!string.IsNullOrEmpty(newProfile.UserName) && profile.UserName != newProfile.UserName)
        {
            // Check if the new username already exists (case-sensitive)
            bool usernameExists = await _cnx.Profile.AnyAsync(p => p.UserName == newProfile.UserName && p.UserId != userId);
            if (usernameExists)
            {
                return BadRequest("A user with this username already exists.");
            }

            // Validate username format
            if (newProfile.UserName.Length < 3 || newProfile.UserName.Length > 16)
            {
                return BadRequest("Username must be between 3 and 16 characters.");
            }

            if (!System.Text.RegularExpressions.Regex.IsMatch(newProfile.UserName, @"^[A-Za-z0-9_]+$"))
            {
                return BadRequest("Username can only contain letters, numbers, and underscores.");
            }

            profile.UserName = newProfile.UserName;
        }

        profile.User.FirstName = newProfile.FirstName;
        profile.User.LastName = newProfile.LastName;
        profile.BirthDate = newProfile.BirthDate;
        profile.Gender = newProfile.Gender;

        // Only update profile picture if provided
        if (!string.IsNullOrEmpty(newProfile.ProfilePicture))
        {
            // Validate that it's a valid base64 image
            if (newProfile.ProfilePicture.StartsWith("data:image/"))
            {
                profile.ProfilePicture = newProfile.ProfilePicture;
            }
            else
            {
                return BadRequest("Invalid profile picture format");
            }
        }

        // Only update cover picture if provided (cover picture is optional)
        if (!string.IsNullOrEmpty(newProfile.ProfileCoverPicture))
        {
            // Validate that it's a valid base64 image
            if (newProfile.ProfileCoverPicture.StartsWith("data:image/"))
            {
                profile.ProfileCoverPicture = newProfile.ProfileCoverPicture;
            }
            else
            {
                return BadRequest("Invalid cover picture format");
            }
        }

        if (!string.IsNullOrEmpty(newProfile.Occupation))
        {
            if (newProfile.Occupation.Length < 3 || newProfile.Occupation.Length > 100)
            {
                return BadRequest("Occupation must be between 3 and 100 characters.");
            }

            if (!System.Text.RegularExpressions.Regex.IsMatch(newProfile.Occupation, @"^[A-Za-z0-9\s\-_.,/|&()@#$%^*+=!?:;'""\[\]{}~`]+$"))
            {
                return BadRequest("Occupation contains invalid characters. Only letters, numbers, spaces, and common special characters are allowed.");
            }
        }

        profile.Occupation = newProfile.Occupation;
        profile.isSearch = newProfile.isSearch;
        profile.Country = newProfile.Country;

        try
        {
            await _cnx.SaveChangesAsync();
            return Ok(new { message = "Profile updated successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to save profile changes", details = ex.Message });
        }
    }

}
