import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Ty<PERSON>graphy,
  Box,
  TextField,
  InputAdornment,
  Divider,
  CardHeader,
  CardContent,
  Dialog,
  DialogContent,
  IconButton,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import CloseIcon from "@mui/icons-material/Close";
import { toast } from "react-toastify";

export default function CheckoutReserved({
  onApply,
  ShowCouponSection,
  onClose,
}) {
  const [coupon, setCoupon] = useState("");
  const [showCouponSection, setShowCouponSection] = useState(
    ShowCouponSection ? ShowCouponSection : false
  );

  const handleApplyClick = async () => {
    // Enhanced input validation
    if (!coupon || typeof coupon !== "string" || coupon.trim() === "") {
      toast.error("Please enter a valid coupon serial key", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    // Validate serial key format (basic validation)
    const trimmedCoupon = coupon.trim();
    if (trimmedCoupon.length < 3) {
      toast.error("Coupon serial key must be at least 3 characters long", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    try {
      await onApply(trimmedCoupon);

      // Clear the coupon input and close dialog on success
      setCoupon("");
      if (onClose) {
        onClose();
      }
    } catch (error) {
      // Enhanced error handling
      let errorMessage = "Failed to reserve coupon. Please try again.";

      if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  const handleAddCouponClick = () => {
    setShowCouponSection(true);
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="sm" fullWidth>
      <IconButton
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          zIndex: 1300, // Higher than dialog content
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          "&:hover": {
            backgroundColor: "rgba(255, 255, 255, 1)",
          },
        }}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>
      <DialogContent>
        <Card sx={{ mb: 3, boxShadow: "none" }}>
          <CardHeader
            title="Order Reserved Coupon"
            subheader="Customer-provided Coupon for the Client's Evaluation of the Service Rendered"
          />

          <CardContent>
            <Stack spacing={2}>
              <Divider />

              {showCouponSection && (
                <>
                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="subtitle1">Coupon :</Typography>
                    <Box sx={{ textAlign: "right" }}>
                      <Typography
                        variant="subtitle1"
                        sx={{ color: "error.main" }}
                      >
                        {coupon ? " " : "No coupon applied"}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{ fontStyle: "italic" }}
                      >
                        (Used only one time)
                      </Typography>
                    </Box>
                  </Stack>

                  <TextField
                    fullWidth
                    placeholder="Coupons codes /rates"
                    value={coupon}
                    onChange={(e) => setCoupon(e.target.value)}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Button
                            type="button"
                            onClick={handleApplyClick}
                            sx={{ mr: -0.5 }}
                          >
                            Apply
                          </Button>
                        </InputAdornment>
                      ),
                    }}
                  />
                </>
              )}
              {!showCouponSection && (
                <Button
                  onClick={handleAddCouponClick}
                  variant="contained"
                  color="primary"
                  startIcon={<EditIcon />}
                >
                  Add Coupon
                </Button>
              )}
            </Stack>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
