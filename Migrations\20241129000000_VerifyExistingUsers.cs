using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace idigix.Migrations
{
    /// <inheritdoc />
    public partial class VerifyExistingUsers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update all existing users to be verified
            migrationBuilder.Sql("UPDATE Users SET IsVerified = 1 WHERE IsVerified = 0;");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Rollback: Set all users back to unverified (optional - be careful with this)
            // migrationBuilder.Sql("UPDATE Users SET IsVerified = 0;");
        }
    }
}
