import { Helmet } from "react-helmet-async";
import { styled } from "@mui/material/styles";
import { Typography, Container, Box, Button, TextField } from "@mui/material";
import SeverErrorIllustration from "../components/SeverErrorIllustration";
import Logo from "../components/Logo";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { resendVerificationEmail } from "../AuthenticationData.ts";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const StyledContent = styled("div")(({ theme }) => ({
  maxWidth: 480,
  margin: "auto",
  minHeight: "100vh",
  display: "flex",
  justifyContent: "center",
  flexDirection: "column",
  padding: theme.spacing(12, 0),
}));

const VerifyMail = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [isResending, setIsResending] = useState(false);

  const handleResendEmail = async () => {
    if (!email.trim()) {
      toast.error("Please enter your email address", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    setIsResending(true);
    try {
      const response = await resendVerificationEmail(email);
      if (response.error) {
        toast.error(response.error, {
          position: "top-center",
          autoClose: 3000,
        });
      } else {
        toast.success(
          "Verification email sent successfully! Please check your inbox.",
          {
            position: "top-center",
            autoClose: 3000,
          }
        );
      }
    } catch (error) {
      toast.error("Failed to resend verification email. Please try again.", {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>IDigics | Verify Email</title>
      </Helmet>
      <Box sx={{ px: 3, py: 2, display: "inline-flex" }}>
        <Logo />
      </Box>
      <Container sx={{ marginTop: "-100px" }}>
        <StyledContent sx={{ textAlign: "center", alignItems: "center" }}>
          <Typography variant="h3" paragraph color="primary">
            Verify Your Email
          </Typography>

          <Typography sx={{ color: "text.secondary" }}>
            please check your email and click the link we just sent you.
          </Typography>

          <Typography sx={{ color: "text.secondary" }}>
            this email may come in the SPAM section !
          </Typography>

          <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />

          <Box sx={{ width: "100%", maxWidth: 400, mb: 3 }}>
            <TextField
              fullWidth
              label="Enter your email to resend verification"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              sx={{ mb: 2 }}
            />
            <Button
              fullWidth
              size="large"
              variant="outlined"
              onClick={handleResendEmail}
              disabled={isResending}
              sx={{ mb: 2 }}
            >
              {isResending ? "Sending..." : "Resend Verification Email"}
            </Button>
          </Box>

          <Button
            size="large"
            variant="contained"
            onClick={() => {
              navigate("/Login");
            }}
          >
            Go to login page
          </Button>
          <ToastContainer />
        </StyledContent>
      </Container>
    </>
  );
};

export default VerifyMail;
