{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\pages\\\\VerifyMailPage.js\",\n  _s = $RefreshSig$();\nimport { Helmet } from \"react-helmet-async\";\nimport { styled } from \"@mui/material/styles\";\nimport { Typography, Container, Box, Button, TextField } from \"@mui/material\";\nimport SeverErrorIllustration from \"../components/SeverErrorIllustration\";\nimport Logo from \"../components/Logo\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useState } from \"react\";\nimport { resendVerificationEmail } from \"../AuthenticationData.ts\";\nimport { ToastContainer, toast } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StyledContent = styled(\"div\")(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    maxWidth: 480,\n    margin: \"auto\",\n    minHeight: \"100vh\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    flexDirection: \"column\",\n    padding: theme.spacing(12, 0)\n  };\n});\n_c = StyledContent;\nconst VerifyMail = () => {\n  _s();\n  const navigate = useNavigate();\n  const [email, setEmail] = useState(\"\");\n  const [isResending, setIsResending] = useState(false);\n  const handleResendEmail = async () => {\n    if (!email.trim()) {\n      toast.error(\"Please enter your email address\", {\n        position: \"top-center\",\n        autoClose: 2000\n      });\n      return;\n    }\n    setIsResending(true);\n    try {\n      const response = await resendVerificationEmail(email);\n      if (response.error) {\n        toast.error(response.error, {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n      } else {\n        toast.success(\"Verification email sent successfully! Please check your inbox.\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n      }\n    } catch (error) {\n      toast.error(\"Failed to resend verification email. Please try again.\", {\n        position: \"top-center\",\n        autoClose: 3000\n      });\n    } finally {\n      setIsResending(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: /*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"IDigics | Verify Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        px: 3,\n        py: 2,\n        display: \"inline-flex\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Logo, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      sx: {\n        marginTop: \"-100px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(StyledContent, {\n        sx: {\n          textAlign: \"center\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          paragraph: true,\n          color: \"primary\",\n          children: \"Verify Your Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: \"text.secondary\"\n          },\n          children: \"please check your email and click the link we just sent you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: \"text.secondary\"\n          },\n          children: \"this email may come in the SPAM section !\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SeverErrorIllustration, {\n          sx: {\n            height: 260,\n            my: {\n              xs: 5,\n              sm: 10\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: \"100%\",\n            maxWidth: 400,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Enter your email to resend verification\",\n            type: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            size: \"large\",\n            variant: \"outlined\",\n            onClick: handleResendEmail,\n            disabled: isResending,\n            sx: {\n              mb: 2\n            },\n            children: isResending ? \"Sending...\" : \"Resend Verification Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          variant: \"contained\",\n          onClick: () => {\n            navigate(\"/Login\");\n          },\n          children: \"Go to login page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(VerifyMail, \"2OYtNZRd3y/MCSO1pwYm94A6KSs=\", false, function () {\n  return [useNavigate];\n});\n_c2 = VerifyMail;\nexport default VerifyMail;\nvar _c, _c2;\n$RefreshReg$(_c, \"StyledContent\");\n$RefreshReg$(_c2, \"VerifyMail\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "styled", "Typography", "Container", "Box", "<PERSON><PERSON>", "TextField", "SeverErrorIllustration", "Logo", "useNavigate", "useState", "resendVerificationEmail", "ToastContainer", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Styled<PERSON>ontent", "_ref", "theme", "max<PERSON><PERSON><PERSON>", "margin", "minHeight", "display", "justifyContent", "flexDirection", "padding", "spacing", "_c", "VerifyMail", "_s", "navigate", "email", "setEmail", "isResending", "setIsResending", "handleResendEmail", "trim", "error", "position", "autoClose", "response", "success", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "px", "py", "marginTop", "textAlign", "alignItems", "variant", "paragraph", "color", "height", "my", "xs", "sm", "width", "mb", "fullWidth", "label", "type", "value", "onChange", "e", "target", "size", "onClick", "disabled", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/pages/VerifyMailPage.js"], "sourcesContent": ["import { Helmet } from \"react-helmet-async\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport { Typography, Container, Box, Button, TextField } from \"@mui/material\";\r\nimport SeverErrorIllustration from \"../components/SeverErrorIllustration\";\r\nimport Logo from \"../components/Logo\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useState } from \"react\";\r\nimport { resendVerificationEmail } from \"../AuthenticationData.ts\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\nconst StyledContent = styled(\"div\")(({ theme }) => ({\r\n  maxWidth: 480,\r\n  margin: \"auto\",\r\n  minHeight: \"100vh\",\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  flexDirection: \"column\",\r\n  padding: theme.spacing(12, 0),\r\n}));\r\n\r\nconst VerifyMail = () => {\r\n  const navigate = useNavigate();\r\n  const [email, setEmail] = useState(\"\");\r\n  const [isResending, setIsResending] = useState(false);\r\n\r\n  const handleResendEmail = async () => {\r\n    if (!email.trim()) {\r\n      toast.error(\"Please enter your email address\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsResending(true);\r\n    try {\r\n      const response = await resendVerificationEmail(email);\r\n      if (response.error) {\r\n        toast.error(response.error, {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n      } else {\r\n        toast.success(\r\n          \"Verification email sent successfully! Please check your inbox.\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          }\r\n        );\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to resend verification email. Please try again.\", {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n    } finally {\r\n      setIsResending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>IDigics | Verify Email</title>\r\n      </Helmet>\r\n      <Box sx={{ px: 3, py: 2, display: \"inline-flex\" }}>\r\n        <Logo />\r\n      </Box>\r\n      <Container sx={{ marginTop: \"-100px\" }}>\r\n        <StyledContent sx={{ textAlign: \"center\", alignItems: \"center\" }}>\r\n          <Typography variant=\"h3\" paragraph color=\"primary\">\r\n            Verify Your Email\r\n          </Typography>\r\n\r\n          <Typography sx={{ color: \"text.secondary\" }}>\r\n            please check your email and click the link we just sent you.\r\n          </Typography>\r\n\r\n          <Typography sx={{ color: \"text.secondary\" }}>\r\n            this email may come in the SPAM section !\r\n          </Typography>\r\n\r\n          <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />\r\n\r\n          <Box sx={{ width: \"100%\", maxWidth: 400, mb: 3 }}>\r\n            <TextField\r\n              fullWidth\r\n              label=\"Enter your email to resend verification\"\r\n              type=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              sx={{ mb: 2 }}\r\n            />\r\n            <Button\r\n              fullWidth\r\n              size=\"large\"\r\n              variant=\"outlined\"\r\n              onClick={handleResendEmail}\r\n              disabled={isResending}\r\n              sx={{ mb: 2 }}\r\n            >\r\n              {isResending ? \"Sending...\" : \"Resend Verification Email\"}\r\n            </Button>\r\n          </Box>\r\n\r\n          <Button\r\n            size=\"large\"\r\n            variant=\"contained\"\r\n            onClick={() => {\r\n              navigate(\"/Login\");\r\n            }}\r\n          >\r\n            Go to login page\r\n          </Button>\r\n          <ToastContainer />\r\n        </StyledContent>\r\n      </Container>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VerifyMail;\r\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,UAAU,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC7E,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,uBAAuB,QAAQ,0BAA0B;AAClE,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,aAAa,GAAGjB,MAAM,CAAC,KAAK,CAAC,CAACkB,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClDE,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,aAAa,EAAE,QAAQ;IACvBC,OAAO,EAAEP,KAAK,CAACQ,OAAO,CAAC,EAAE,EAAE,CAAC;EAC9B,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAREX,aAAa;AAUnB,MAAMY,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACJ,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;MACjBzB,KAAK,CAAC0B,KAAK,CAAC,iCAAiC,EAAE;QAC7CC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IAEAL,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM/B,uBAAuB,CAACsB,KAAK,CAAC;MACrD,IAAIS,QAAQ,CAACH,KAAK,EAAE;QAClB1B,KAAK,CAAC0B,KAAK,CAACG,QAAQ,CAACH,KAAK,EAAE;UAC1BC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACL5B,KAAK,CAAC8B,OAAO,CACX,gEAAgE,EAChE;UACEH,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CACF,CAAC;MACH;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd1B,KAAK,CAAC0B,KAAK,CAAC,wDAAwD,EAAE;QACpEC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,SAAS;MACRL,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACErB,OAAA,CAAAE,SAAA;IAAA2B,QAAA,gBACE7B,OAAA,CAACf,MAAM;MAAA4C,QAAA,eACL7B,OAAA;QAAA6B,QAAA,EAAO;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACTjC,OAAA,CAACX,GAAG;MAAC6C,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAE3B,OAAO,EAAE;MAAc,CAAE;MAAAoB,QAAA,eAChD7B,OAAA,CAACP,IAAI;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNjC,OAAA,CAACZ,SAAS;MAAC8C,EAAE,EAAE;QAAEG,SAAS,EAAE;MAAS,CAAE;MAAAR,QAAA,eACrC7B,OAAA,CAACG,aAAa;QAAC+B,EAAE,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAV,QAAA,gBAC/D7B,OAAA,CAACb,UAAU;UAACqD,OAAO,EAAC,IAAI;UAACC,SAAS;UAACC,KAAK,EAAC,SAAS;UAAAb,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACb,UAAU;UAAC+C,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAiB,CAAE;UAAAb,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACb,UAAU;UAAC+C,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAiB,CAAE;UAAAb,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACR,sBAAsB;UAAC0C,EAAE,EAAE;YAAES,MAAM,EAAE,GAAG;YAAEC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAG;UAAE;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEtEjC,OAAA,CAACX,GAAG;UAAC6C,EAAE,EAAE;YAAEa,KAAK,EAAE,MAAM;YAAEzC,QAAQ,EAAE,GAAG;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBAC/C7B,OAAA,CAACT,SAAS;YACR0D,SAAS;YACTC,KAAK,EAAC,yCAAyC;YAC/CC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAElC,KAAM;YACbmC,QAAQ,EAAGC,CAAC,IAAKnC,QAAQ,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1ClB,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAE;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFjC,OAAA,CAACV,MAAM;YACL2D,SAAS;YACTO,IAAI,EAAC,OAAO;YACZhB,OAAO,EAAC,UAAU;YAClBiB,OAAO,EAAEnC,iBAAkB;YAC3BoC,QAAQ,EAAEtC,WAAY;YACtBc,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAE,CAAE;YAAAnB,QAAA,EAEbT,WAAW,GAAG,YAAY,GAAG;UAA2B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjC,OAAA,CAACV,MAAM;UACLkE,IAAI,EAAC,OAAO;UACZhB,OAAO,EAAC,WAAW;UACnBiB,OAAO,EAAEA,CAAA,KAAM;YACbxC,QAAQ,CAAC,QAAQ,CAAC;UACpB,CAAE;UAAAY,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACH,cAAc;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eACZ,CAAC;AAEP,CAAC;AAACjB,EAAA,CApGID,UAAU;EAAA,QACGrB,WAAW;AAAA;AAAAiE,GAAA,GADxB5C,UAAU;AAsGhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAA6C,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}