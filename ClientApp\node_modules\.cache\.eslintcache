[{"C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\reportWebVitals.js": "4", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\routes.js": "5", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\BudgetContext.js": "6", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\SearchContext.js": "7", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\ProfileContext.js": "8", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\index.js": "9", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\index.js": "10", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\scroll-to-top\\index.js": "11", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\AuthenticationData.ts": "12", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\SearchData.ts": "13", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\ProfileData.ts": "14", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Page404.js": "15", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\AccessDenied.js": "16", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifyMailPage.js": "17", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\LoginPage.js": "18", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifyPasswordChangingPage.js": "19", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\ForgotPasswordEmail.js": "20", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifiyingMailPage.js": "21", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SignUpPage.js": "22", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\BundlesPage.js": "23", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\AnalyticsPage.js": "24", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SettingsPage.js": "25", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\TrackPage.js": "26", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\RatingPage.js": "27", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\profileUser.js": "28", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SearchResults.js": "29", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Profile.js": "30", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\AppManageCoupons.js": "31", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\globalStyles.js": "32", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\customShadows.js": "33", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\palette.js": "34", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\shadows.js": "35", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\typography.js": "36", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\styles.js": "38", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\useChart.js": "39", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\scroll-to-top\\ScrollToTop.js": "40", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Api.js": "41", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\config.js": "42", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\AnalyticsData.ts": "43", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\PurchasesData.ts": "44", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\CouponsData.ts": "45", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\ContactData.ts": "46", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\LinkData.ts": "47", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\index.js": "48", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\Logo.js": "49", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\SeverErrorIllustration.js": "50", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\PageNotFoundIllustration.js": "51", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\BookingCustomerReviews.js": "52", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\EmptyContent.js": "53", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\InviterFriends.js": "54", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\VerticalLinearStepper.js": "55", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\hooks\\useResponsive.js": "56", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SearchNotFound.js": "57", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Appearance.js": "58", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppTasks.js": "59", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentVisits.js": "60", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppWebsiteVisits.js": "61", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppPurchasesTab.js": "62", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppConversionRates.js": "63", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppDepositsTab.js": "64", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByProfile.js": "65", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppWidgetSummary.js": "66", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReviewTrack.js": "67", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\CheckoutReserved.js": "68", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\formatTime.js": "69", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\RatingDialog.js": "70", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Account\\AccountSettings.js": "71", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\PhoneLinkDialog.js": "72", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\cssStyles.js": "73", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\DashboardLayout.js": "74", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\WhatsAppLinkDialog.js": "75", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\PhotoSelector.js": "76", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\index.js": "77", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\login\\index.js": "78", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\index.js": "79", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Input.js": "80", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Table.js": "81", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Card.js": "82", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Paper.js": "83", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Tooltip.js": "84", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\formatNumber.js": "85", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Autocomplete.js": "86", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Button.js": "87", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Backdrop.js": "88", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Typography.js": "89", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\SquarePhotoSelector.js": "90", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\iconify\\index.js": "91", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\ThankYouCard.js": "92", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Contact\\AddCvDialog.js": "93", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppNewsUpdate.js": "94", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentSubject.js": "95", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppTrafficBySite.js": "96", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByPublicProfile.js": "97", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppOrderTimeline.js": "98", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\login\\LoginForm.js": "99", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppBundleWidget.js": "100", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register1.js": "101", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\SignUpForm.js": "102", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register2.js": "103", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register3.js": "104", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppProfileCard.js": "105", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\RegisterConfirm.js": "106", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\index.js": "107", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\nav\\index.js": "108", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\iconify\\Iconify.js": "109", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\Scrollbar.js": "110", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\AccountPopover.js": "111", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\Searchbar.js": "112", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\PalastinePopover.js": "113", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\index.js": "114", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\NavSection.js": "115", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\styles.js": "116", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\EmailLinkDialog.js": "117"}, {"size": 673, "mtime": *************, "results": "118", "hashOfConfig": "119"}, {"size": 1260, "mtime": *************, "results": "120", "hashOfConfig": "119"}, {"size": 5175, "mtime": *************, "results": "121", "hashOfConfig": "119"}, {"size": 377, "mtime": *************, "results": "122", "hashOfConfig": "119"}, {"size": 5999, "mtime": 1753893291894, "results": "123", "hashOfConfig": "119"}, {"size": 679, "mtime": 1753114669319, "results": "124", "hashOfConfig": "119"}, {"size": 1377, "mtime": 1753114669323, "results": "125", "hashOfConfig": "119"}, {"size": 1970, "mtime": 1753114669319, "results": "126", "hashOfConfig": "119"}, {"size": 1214, "mtime": 1753114669474, "results": "127", "hashOfConfig": "119"}, {"size": 248, "mtime": 1753114669340, "results": "128", "hashOfConfig": "119"}, {"size": 42, "mtime": 1753114669362, "results": "129", "hashOfConfig": "119"}, {"size": 8041, "mtime": 1753893291876, "results": "130", "hashOfConfig": "119"}, {"size": 2142, "mtime": 1753114669329, "results": "131", "hashOfConfig": "119"}, {"size": 3061, "mtime": 1753124957410, "results": "132", "hashOfConfig": "119"}, {"size": 1830, "mtime": 1753893291890, "results": "133", "hashOfConfig": "119"}, {"size": 1690, "mtime": 1753114669386, "results": "134", "hashOfConfig": "119"}, {"size": 3730, "mtime": 1753893291894, "results": "135", "hashOfConfig": "119"}, {"size": 2197, "mtime": 1753114669390, "results": "136", "hashOfConfig": "119"}, {"size": 9093, "mtime": 1753641692611, "results": "137", "hashOfConfig": "119"}, {"size": 3750, "mtime": 1753119287395, "results": "138", "hashOfConfig": "119"}, {"size": 2857, "mtime": 1753114669402, "results": "139", "hashOfConfig": "119"}, {"size": 2223, "mtime": 1753119495725, "results": "140", "hashOfConfig": "119"}, {"size": 8060, "mtime": 1753641692611, "results": "141", "hashOfConfig": "119"}, {"size": 13258, "mtime": 1753643236538, "results": "142", "hashOfConfig": "119"}, {"size": 10250, "mtime": 1753114669397, "results": "143", "hashOfConfig": "119"}, {"size": 17622, "mtime": 1753642811092, "results": "144", "hashOfConfig": "119"}, {"size": 39881, "mtime": 1753640861779, "results": "145", "hashOfConfig": "119"}, {"size": 109418, "mtime": 1753893291894, "results": "146", "hashOfConfig": "119"}, {"size": 11132, "mtime": 1753114669397, "results": "147", "hashOfConfig": "119"}, {"size": 58126, "mtime": 1753893291892, "results": "148", "hashOfConfig": "119"}, {"size": 6740, "mtime": 1753893291900, "results": "149", "hashOfConfig": "119"}, {"size": 1306, "mtime": 1753114669474, "results": "150", "hashOfConfig": "119"}, {"size": 1261, "mtime": 1753114669474, "results": "151", "hashOfConfig": "119"}, {"size": 2855, "mtime": 1753114669487, "results": "152", "hashOfConfig": "119"}, {"size": 3000, "mtime": 1753114669488, "results": "153", "hashOfConfig": "119"}, {"size": 2435, "mtime": 1753114669488, "results": "154", "hashOfConfig": "119"}, {"size": 46, "mtime": 1753114669378, "results": "155", "hashOfConfig": "119"}, {"size": 2070, "mtime": 1753114669340, "results": "156", "hashOfConfig": "119"}, {"size": 4415, "mtime": 1753114669340, "results": "157", "hashOfConfig": "119"}, {"size": 335, "mtime": 1753114669362, "results": "158", "hashOfConfig": "119"}, {"size": 2461, "mtime": *************, "results": "159", "hashOfConfig": "119"}, {"size": 390, "mtime": 1753114669323, "results": "160", "hashOfConfig": "119"}, {"size": 8273, "mtime": *************, "results": "161", "hashOfConfig": "119"}, {"size": 2182, "mtime": 1753114669329, "results": "162", "hashOfConfig": "119"}, {"size": 7086, "mtime": 1753459010427, "results": "163", "hashOfConfig": "119"}, {"size": 1956, "mtime": 1753114669319, "results": "164", "hashOfConfig": "119"}, {"size": 5203, "mtime": 1753114669327, "results": "165", "hashOfConfig": "119"}, {"size": 662, "mtime": 1753114669481, "results": "166", "hashOfConfig": "119"}, {"size": 655, "mtime": 1753114669331, "results": "167", "hashOfConfig": "119"}, {"size": 184262, "mtime": 1753114669340, "results": "168", "hashOfConfig": "119"}, {"size": 161314, "mtime": 1753114669340, "results": "169", "hashOfConfig": "119"}, {"size": 12296, "mtime": 1753114669423, "results": "170", "hashOfConfig": "119"}, {"size": 1438, "mtime": 1753114669417, "results": "171", "hashOfConfig": "119"}, {"size": 2672, "mtime": 1753114669441, "results": "172", "hashOfConfig": "119"}, {"size": 2943, "mtime": 1753114669423, "results": "173", "hashOfConfig": "119"}, {"size": 1221, "mtime": *************, "results": "174", "hashOfConfig": "119"}, {"size": 778, "mtime": 1753114669397, "results": "175", "hashOfConfig": "119"}, {"size": 43427, "mtime": 1753893291876, "results": "176", "hashOfConfig": "119"}, {"size": 5347, "mtime": 1753114669439, "results": "177", "hashOfConfig": "119"}, {"size": 2366, "mtime": 1753114669432, "results": "178", "hashOfConfig": "119"}, {"size": 1335, "mtime": 1753114669441, "results": "179", "hashOfConfig": "119"}, {"size": 1689, "mtime": 1753114669438, "results": "180", "hashOfConfig": "119"}, {"size": 1377, "mtime": 1753114669423, "results": "181", "hashOfConfig": "119"}, {"size": 1620, "mtime": 1753114669432, "results": "182", "hashOfConfig": "119"}, {"size": 7808, "mtime": 1753641692622, "results": "183", "hashOfConfig": "119"}, {"size": 2147, "mtime": 1753114669441, "results": "184", "hashOfConfig": "119"}, {"size": 4365, "mtime": 1753114669423, "results": "185", "hashOfConfig": "119"}, {"size": 4729, "mtime": 1753893291901, "results": "186", "hashOfConfig": "119"}, {"size": 663, "mtime": 1753114669495, "results": "187", "hashOfConfig": "119"}, {"size": 17088, "mtime": 1753114669397, "results": "188", "hashOfConfig": "119"}, {"size": 5072, "mtime": 1753114669411, "results": "189", "hashOfConfig": "119"}, {"size": 23054, "mtime": 1753117526026, "results": "190", "hashOfConfig": "119"}, {"size": 2593, "mtime": 1753114669488, "results": "191", "hashOfConfig": "119"}, {"size": 1780, "mtime": 1753114669370, "results": "192", "hashOfConfig": "119"}, {"size": 23570, "mtime": 1753121971458, "results": "193", "hashOfConfig": "119"}, {"size": 4425, "mtime": 1753124877195, "results": "194", "hashOfConfig": "119"}, {"size": 865, "mtime": 1753114669446, "results": "195", "hashOfConfig": "119"}, {"size": 53, "mtime": 1753114669460, "results": "196", "hashOfConfig": "119"}, {"size": 340, "mtime": 1753114669466, "results": "197", "hashOfConfig": "119"}, {"size": 1776, "mtime": 1753114669481, "results": "198", "hashOfConfig": "119"}, {"size": 341, "mtime": 1753114669481, "results": "199", "hashOfConfig": "119"}, {"size": 852, "mtime": 1753114669481, "results": "200", "hashOfConfig": "119"}, {"size": 314, "mtime": 1753114669481, "results": "201", "hashOfConfig": "119"}, {"size": 360, "mtime": 1753114669481, "results": "202", "hashOfConfig": "119"}, {"size": 1382, "mtime": 1753114669493, "results": "203", "hashOfConfig": "119"}, {"size": 289, "mtime": 1753114669474, "results": "204", "hashOfConfig": "119"}, {"size": 1156, "mtime": 1753114669480, "results": "205", "hashOfConfig": "119"}, {"size": 419, "mtime": 1753114669474, "results": "206", "hashOfConfig": "119"}, {"size": 365, "mtime": 1753114669481, "results": "207", "hashOfConfig": "119"}, {"size": 2454, "mtime": 1753124907532, "results": "208", "hashOfConfig": "119"}, {"size": 38, "mtime": 1753114669353, "results": "209", "hashOfConfig": "119"}, {"size": 2005, "mtime": 1753114669423, "results": "210", "hashOfConfig": "119"}, {"size": 8510, "mtime": 1753114669414, "results": "211", "hashOfConfig": "119"}, {"size": 2897, "mtime": 1753114669435, "results": "212", "hashOfConfig": "119"}, {"size": 2301, "mtime": 1753114669431, "results": "213", "hashOfConfig": "119"}, {"size": 1815, "mtime": 1753114669440, "results": "214", "hashOfConfig": "119"}, {"size": 5161, "mtime": 1753114669435, "results": "215", "hashOfConfig": "119"}, {"size": 2128, "mtime": 1753114669435, "results": "216", "hashOfConfig": "119"}, {"size": 7676, "mtime": 1753893291903, "results": "217", "hashOfConfig": "119"}, {"size": 7254, "mtime": 1753641692619, "results": "218", "hashOfConfig": "119"}, {"size": 8106, "mtime": 1753114669460, "results": "219", "hashOfConfig": "119"}, {"size": 3491, "mtime": 1753893291904, "results": "220", "hashOfConfig": "119"}, {"size": 2946, "mtime": 1753114669465, "results": "221", "hashOfConfig": "119"}, {"size": 8389, "mtime": 1753114669466, "results": "222", "hashOfConfig": "119"}, {"size": 3382, "mtime": 1753114669435, "results": "223", "hashOfConfig": "119"}, {"size": 205, "mtime": 1753114669466, "results": "224", "hashOfConfig": "119"}, {"size": 2828, "mtime": 1753114669378, "results": "225", "hashOfConfig": "119"}, {"size": 5620, "mtime": 1753114669381, "results": "226", "hashOfConfig": "119"}, {"size": 647, "mtime": 1753114669352, "results": "227", "hashOfConfig": "119"}, {"size": 1541, "mtime": 1753114669340, "results": "228", "hashOfConfig": "119"}, {"size": 3476, "mtime": 1753114669370, "results": "229", "hashOfConfig": "119"}, {"size": 4045, "mtime": 1753114669378, "results": "230", "hashOfConfig": "119"}, {"size": 4606, "mtime": 1753114669370, "results": "231", "hashOfConfig": "119"}, {"size": 41, "mtime": 1753114669360, "results": "232", "hashOfConfig": "119"}, {"size": 2696, "mtime": 1753114669355, "results": "233", "hashOfConfig": "119"}, {"size": 690, "mtime": 1753114669360, "results": "234", "hashOfConfig": "119"}, {"size": 5825, "mtime": 1753124714386, "results": "235", "hashOfConfig": "119"}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, "9x4rwh", {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "255", "usedDeprecatedRules": "239"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "259", "usedDeprecatedRules": "239"}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "278", "usedDeprecatedRules": "279"}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "304", "usedDeprecatedRules": "239"}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "314", "usedDeprecatedRules": "315"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "319", "usedDeprecatedRules": "239"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "315"}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "332", "usedDeprecatedRules": "239"}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "375", "usedDeprecatedRules": "279"}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "279"}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "279"}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "279"}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "403", "usedDeprecatedRules": "239"}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "446", "usedDeprecatedRules": "239"}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "450", "usedDeprecatedRules": "239"}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "460", "usedDeprecatedRules": "239"}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "530", "usedDeprecatedRules": "239"}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "543", "usedDeprecatedRules": "239"}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "556", "usedDeprecatedRules": "239"}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "563", "usedDeprecatedRules": "239"}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "567", "usedDeprecatedRules": "239"}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "571", "usedDeprecatedRules": "239"}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "575", "usedDeprecatedRules": "239"}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "591", "usedDeprecatedRules": "239"}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "595", "usedDeprecatedRules": "239"}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "599", "usedDeprecatedRules": "239"}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "239"}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\routes.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\BudgetContext.js", ["612"], [], "import React, { createContext, useContext, useState, useEffect } from \"react\";\r\nimport { useProfile } from \"./ProfileContext\";\r\n\r\nconst BudgetContext = createContext();\r\n\r\nexport const useBudget = () => useContext(BudgetContext);\r\n\r\nexport const BudgetProvider = ({ children }) => {\r\n    const { profile } = useProfile();\r\n    const [budget, setBudget] = useState(0.0);\r\n\r\n    useEffect(() => {\r\n        fetchProfile();\r\n    }, [profile]);\r\n\r\n    const fetchProfile = async () => {\r\n        setBudget(profile?.budget);\r\n    };\r\n\r\n    return (\r\n        <BudgetContext.Provider value={{ budget, setBudget }}>\r\n            {children}\r\n        </BudgetContext.Provider>\r\n    );\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\SearchContext.js", ["613"], [], "import React, { createContext, useContext, useState, useEffect } from \"react\";\r\nimport { PostSearch, GetProfileFromSearch } from \"../SearchData.ts\";\r\nimport { useProfile } from \"./ProfileContext\";\r\n\r\nconst SearchContext = createContext();\r\n\r\nexport const useSearch = () => useContext(SearchContext);\r\n\r\nexport const SearchProvider = ({ children }) => {\r\n    const { profile } = useProfile();\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchSearchResults = async (query) => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const response = await GetProfileFromSearch(query);\r\n            setSearchResults(response.data);\r\n            await PostSearch({\r\n                UserId: profile.id,\r\n                Query: query,\r\n                Date: new Date(),\r\n            });\r\n        } catch (err) {\r\n            setError(\"Error fetching search results\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <SearchContext.Provider\r\n            value={{\r\n                searchResults,\r\n                loading,\r\n                error,\r\n                fetchSearchResults,\r\n            }}\r\n        >\r\n            {children}\r\n        </SearchContext.Provider>\r\n    );\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\ProfileContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\scroll-to-top\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\AuthenticationData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\SearchData.ts", ["614"], [], "import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostSearch {\r\n    UserId: number;\r\n    Query: string;\r\n    Date: string;\r\n}\r\n\r\nexport const GetProfileFromSearch = async (search: string) => {\r\n    console.log(search);\r\n    try {\r\n        const response = await api.get(\r\n            `${BASE_URL}/Search/Search`,\r\n            {\r\n                params: {\r\n                    search: search,\r\n                },\r\n                headers: {\r\n                    \"Cache-Control\": \"public,max-age=3600\",\r\n                    Pragma: \"\",\r\n                    withCredentials: true,\r\n                },\r\n            }\r\n        );\r\n\r\n        return response;\r\n    } catch (error) {\r\n        console.log(\"Error:\", error.message);\r\n    }\r\n};\r\n\r\nexport const PostSearch = async (search: PostSearch) => {\r\n    const data = {\r\n        UserId: search.UserId,\r\n        Query: search.Query,\r\n        Date: search.Date,\r\n    };\r\n\r\n    try {\r\n        var authToken = getAuthToken();\r\n        const response = await api.post(`${BASE_URL}/Search/PostSearch`, data, {\r\n            headers: {\r\n                Authorization: `Bearer ${authToken}`,\r\n                \"Content-Type\": \"application/json\",\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return { error: error.message };\r\n    }\r\n};\r\n\r\nexport const GetSearchQueries = async () => {\r\n    try {\r\n        var authToken = getAuthToken();\r\n        const response = await api.get(`${BASE_URL}/Search/GetSearchQueries`, {\r\n            headers: {\r\n                Authorization: `Bearer ${authToken}`,\r\n                \"Content-Type\": \"application/json\",\r\n            },\r\n        });\r\n\r\n        return response;\r\n    } catch (error) {\r\n        return { error: error.message };\r\n    }\r\n};\r\n\r\nexport function getAuthToken() {\r\n    const cookies = document.cookie.split(\";\");\r\n\r\n    for (let i = 0; i < cookies.length; i++) {\r\n        const cookie = cookies[i].trim();\r\n        if (cookie.startsWith(\"authToken=\")) {\r\n            return cookie.substring(\"authToken=\".length, cookie.length);\r\n        }\r\n    }\r\n\r\n    return null;\r\n}\r\n", [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\ProfileData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Page404.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\AccessDenied.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifyMailPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifyPasswordChangingPage.js", ["615", "616"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\ForgotPasswordEmail.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\VerifiyingMailPage.js", ["617"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport {\r\n  Typography,\r\n  Container,\r\n  CircularProgress,\r\n  Button,\r\n  Box,\r\n} from \"@mui/material\";\r\nimport SeverErrorIllustration from \"../components/SeverErrorIllustration\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { verifyEmail } from \"../AuthenticationData.ts\";\r\nimport Logo from \"../components/Logo\";\r\n\r\nconst StyledContent = styled(\"div\")(({ theme }) => ({\r\n  maxWidth: 480,\r\n  margin: \"auto\",\r\n  minHeight: \"100vh\",\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  flexDirection: \"column\",\r\n  padding: theme.spacing(12, 0),\r\n}));\r\n\r\nconst VerifyingMail = () => {\r\n  const navigate = useNavigate();\r\n  const { token } = useParams();\r\n\r\n  const [verificationStatus, setVerificationStatus] = useState(\"pending\");\r\n\r\n  useEffect(() => {\r\n    verify();\r\n  }, []);\r\n\r\n  const verify = async () => {\r\n    try {\r\n      const response = await verifyEmail(token);\r\n      if (response != null) {\r\n        setVerificationStatus(\"success\");\r\n      }\r\n      if (response.error) {\r\n        setVerificationStatus(\"error\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error verifying email:\", error);\r\n      setVerificationStatus(\"error\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>IDigics | Verifying Email</title>\r\n      </Helmet>\r\n\r\n      <Box sx={{ px: 3, py: 2, display: \"inline-flex\" }}>\r\n        <Logo />\r\n      </Box>\r\n\r\n      <Container sx={{ marginTop: \"-100px\" }}>\r\n        <StyledContent sx={{ textAlign: \"center\", alignItems: \"center\" }}>\r\n          <Typography variant=\"h3\" paragraph color=\"primary\">\r\n            Verifying Your Email\r\n          </Typography>\r\n\r\n          <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />\r\n\r\n          {verificationStatus === \"success\" && (\r\n            <>\r\n              <Typography sx={{ color: \"text.secondary\" }}>\r\n                Thank you for verifiying your email\r\n              </Typography>\r\n              <Typography sx={{ color: \"text.secondary\" }}>\r\n                Please enjoy using IDigics\r\n              </Typography>\r\n\r\n              <Button\r\n                size=\"large\"\r\n                variant=\"contained\"\r\n                onClick={() => {\r\n                  navigate(\"/Login\");\r\n                }}\r\n              >\r\n                Home\r\n              </Button>\r\n            </>\r\n          )}\r\n          {verificationStatus === \"pending\" && <CircularProgress />}\r\n          {verificationStatus === \"error\" && (\r\n            <Typography variant=\"body1\" color=\"error\">\r\n              Error verifying email. Please try again later.\r\n            </Typography>\r\n          )}\r\n        </StyledContent>\r\n      </Container>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VerifyingMail;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SignUpPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\BundlesPage.js", ["618"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\AnalyticsPage.js", ["619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport { useTheme } from \"@mui/material/styles\";\r\nimport { useEffect, useState, lazy, Suspense, startTransition } from \"react\";\r\nimport {\r\n  GetClicks,\r\n  GetViews,\r\n  calculateDailyClicks,\r\n  calculateCountryClicks,\r\n  calculateGenderClicks,\r\n  calculateDailyViews,\r\n  GetCategoryClickCounts,\r\n  getClickCountsByCategory,\r\n} from \"../AnalyticsData.ts\";\r\nimport { Container, Grid, Typography, CircularProgress, Box } from \"@mui/material\";\r\nimport { motion } from \"framer-motion\";\r\n\r\n// Lazy load components\r\nconst AppWidgetSummary = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppWidgetSummary\")\r\n);\r\nconst AppConversionRates = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppConversionRates\")\r\n);\r\nconst AppCurrentVisits = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppCurrentVisits\")\r\n);\r\nconst AppWebsiteVisits = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppWebsiteVisits\")\r\n);\r\n\r\nconst EmptyContent = lazy(() =>\r\n  import(\"../sections/@dashboard/Coupons/EmptyContent.js\")\r\n);\r\n\r\nconst AppTasks = lazy(() => import(\"../sections/@dashboard/app/AppTasks\"));\r\n\r\nexport default function AnalyticsPage() {\r\n  const theme = useTheme();\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [Clicks, setClicks] = useState([]);\r\n  const [Views, setViews] = useState([]);\r\n  const [Daily, setDaily] = useState([]);\r\n  const [CategoryClicks, setCategoryClicks] = useState([]);\r\n  const [Country, setCountry] = useState([]);\r\n  const [Gender, setGender] = useState([]);\r\n  const [LinksTotalCount, setLinksTotalCount] = useState([]);\r\n  const [DailyViews, setDailyViews] = useState([]);\r\n\r\n  useEffect(() => {\r\n    startTransition(async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        await Promise.all([fetchClicksData(), fetchViewsData()]);\r\n      } catch (error) {\r\n        console.error(\"Error fetching analytics data:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  const fetchClicksData = async () => {\r\n    try {\r\n      const response = await GetClicks();\r\n      setClicks(response.data);\r\n      setDaily(calculateDailyClicks(response.data));\r\n      setCountry(calculateCountryClicks(response.data));\r\n      setGender(calculateGenderClicks(response.data));\r\n      setCategoryClicks(getClickCountsByCategory(response.data));\r\n      setLinksTotalCount(GetCategoryClickCounts(response.data));\r\n    } catch (error) {\r\n      console.error(\"Error fetching Clicks data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchViewsData = async () => {\r\n    try {\r\n      const response = await GetViews();\r\n      setViews(response.data);\r\n      setDailyViews(calculateDailyViews(response.data));\r\n    } catch (error) {\r\n      console.error(\"Error fetching Views data:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Helmet>\r\n        <title> IDigics | Analytics </title>\r\n      </Helmet>\r\n      <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n        Analytics\r\n      </Typography>\r\n\r\n      <Suspense fallback={<CircularProgress />}>\r\n        <Container maxWidth=\"xl\">\r\n          {isLoading ? (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{\r\n                duration: 0.5,\r\n                ease: \"easeOut\",\r\n              }}\r\n            >\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  minHeight: \"400px\",\r\n                  flexDirection: \"column\",\r\n                  gap: 2,\r\n                }}\r\n              >\r\n                <CircularProgress size={60} />\r\n                <Typography variant=\"h6\" color=\"textSecondary\">\r\n                  Loading analytics data...\r\n                </Typography>\r\n              </Box>\r\n            </motion.div>\r\n          ) : Views.length == 0 || Clicks.length == 0 ? (\r\n            <EmptyContent\r\n              title=\"No Analytics Data Available\"\r\n              description=\"It looks like there hasn't been any traffic yet. As soon as you start getting clicks and views, your analytics will populate here.\"\r\n              img=\"/assets/illustrations/illustration_empty_cart.svg\"\r\n            />\r\n          ) : (\r\n            <Grid container spacing={3}>\r\n              <Grid item xs={12} sm={6} md={6}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppWidgetSummary\r\n                    title=\"Total CLicks\"\r\n                    total={Clicks.reduce(\r\n                      (total, click) => total + click.clickCount,\r\n                      0\r\n                    )}\r\n                    color={\"info\"}\r\n                    icon={\"ant-design:edit-filled\"}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} sm={6} md={6}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppWidgetSummary\r\n                    title=\"Total Views\"\r\n                    total={Views.reduce(\r\n                      (total, view) => total + view.viewsCount,\r\n                      0\r\n                    )}\r\n                    color=\"error\"\r\n                    icon={\"ant-design:eye-filled\"}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              {/* <Grid item xs={12} sm={6} md={4}>\r\n                                <Suspense fallback={<CircularProgress />}>\r\n                                    <AppWidgetSummary\r\n                                        title=\"Most Viewed Country\"\r\n                                        total={\r\n                                            MostClickedCountry(Clicks) || \"-\"\r\n                                        } // Set \"-\" as the default value\r\n                                        color=\"success\"\r\n                                        icon={\"ant-design:flag-filled\"}\r\n                                    />\r\n                                </Suspense>\r\n                            </Grid> */}\r\n\r\n              <Grid item xs={12} md={6} lg={8}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppConversionRates\r\n                    title=\"Daily Views\"\r\n                    subheader=\"Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data.\"\r\n                    chartData={DailyViews.map((item) => ({\r\n                      label: item.date,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={4}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppCurrentVisits\r\n                    title=\"Countries\"\r\n                    subheader=\"Discover the top countries where your links\"\r\n                    chartData={Country.map((item) => ({\r\n                      label: item.country,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                    chartColors={[\r\n                      theme.palette.success.main,\r\n                      theme.palette.warning.main,\r\n                      theme.palette.text.secondary,\r\n                      theme.palette.info.main,\r\n                      theme.palette.primary.main,\r\n                      theme.palette.error.main,\r\n                    ]}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={8}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppConversionRates\r\n                    title=\"Daily Engagement\"\r\n                    subheader=\"Explore daily engagement metrics to track the interaction and activity of users with your content. Understand when your audience is most engaged, helping you optimize your strategies for higher user interaction and better overall engagement.\"\r\n                    chartData={Daily.map((item) => ({\r\n                      label: item.date,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={4}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppCurrentVisits\r\n                    title=\"Genders\"\r\n                    subheader=\"Explore the distribution of link clicks based on gender\"\r\n                    chartData={Gender.map((item) => ({\r\n                      label: item.gender,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                    chartColors={[\r\n                      theme.palette.error.main,\r\n                      theme.palette.secondary.main,\r\n                    ]}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              {/* <Grid item xs={12} md={6} lg={8}>\r\n                            <Suspense fallback={<CircularProgress />}>\r\n                                <AppWebsiteVisits\r\n                                    title=\"Website Visits\"\r\n                                    subheader=\"(+43%) than last year\"\r\n                                    chartLabels={[\r\n                                        \"01/01/2003\",\r\n                                        \"02/01/2003\",\r\n                                        \"03/01/2003\",\r\n                                        \"04/01/2003\",\r\n                                        \"05/01/2003\",\r\n                                        \"06/01/2003\",\r\n                                        \"07/01/2003\",\r\n                                        \"08/01/2003\",\r\n                                        \"09/01/2003\",\r\n                                        \"10/01/2003\",\r\n                                        \"11/01/2003\",\r\n                                    ]}\r\n                                    chartData={[\r\n                                        {\r\n                                            name: \"Team A\",\r\n                                            type: \"column\",\r\n                                            fill: \"solid\",\r\n                                            data: [\r\n                                                23, 11, 22, 27, 13, 22, 37, 21,\r\n                                                44, 22, 30,\r\n                                            ],\r\n                                        },\r\n                                        {\r\n                                            name: \"Team B\",\r\n                                            type: \"area\",\r\n                                            fill: \"gradient\",\r\n                                            data: [\r\n                                                44, 55, 41, 67, 22, 43, 21, 41,\r\n                                                56, 27, 43,\r\n                                            ],\r\n                                        },\r\n                                        {\r\n                                            name: \"Team C\",\r\n                                            type: \"line\",\r\n                                            fill: \"solid\",\r\n                                            data: [\r\n                                                30, 25, 36, 30, 45, 35, 64, 52,\r\n                                                59, 36, 39,\r\n                                            ],\r\n                                        },\r\n                                    ]}\r\n                                />\r\n                            </Suspense>\r\n                        </Grid> */}\r\n\r\n              {/* <Grid item xs={12} md={6} lg={8}>\r\n                            <Suspense fallback={<CircularProgress />}>\r\n                                <AppTasks\r\n                                    title=\"Tasks\"\r\n                                    list={[\r\n                                        {\r\n                                            id: \"1\",\r\n                                            label: \"Create FireStone Logo\",\r\n                                        },\r\n                                        {\r\n                                            id: \"2\",\r\n                                            label: \"Add SCSS and JS files if required\",\r\n                                        },\r\n                                        {\r\n                                            id: \"3\",\r\n                                            label: \"Stakeholder Meeting\",\r\n                                        },\r\n                                        {\r\n                                            id: \"4\",\r\n                                            label: \"Scoping & Estimations\",\r\n                                        },\r\n                                        { id: \"5\", label: \"Sprint Showcase\" },\r\n                                    ]}\r\n                                />\r\n                            </Suspense>\r\n                        </Grid> */}\r\n            </Grid>\r\n          )}\r\n        </Container>\r\n      </Suspense>\r\n    </Container>\r\n  );\r\n}\r\n", [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SettingsPage.js", ["628", "629", "630", "631", "632", "633"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport { useCallback, useState, lazy, startTransition, Suspense } from \"react\";\r\n\r\nimport { Grid, Typography, CircularProgress } from \"@mui/material\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nimport {\r\n  Card,\r\n  But<PERSON>,\r\n  Box,\r\n  CardContent,\r\n  Container,\r\n  CardHeader,\r\n  Tab,\r\n  Tabs,\r\n} from \"@mui/material\";\r\n\r\nimport { GetDeposits, GetPurchases } from \"../PurchasesData.ts\";\r\nimport { useEffect } from \"react\";\r\n\r\nimport AppPurchasesTab from \"../sections/@dashboard/app/AppPurchasesTab\";\r\n// import ArrowForwardIcon from \"@mui/icons-material/ArrowForward\";\r\nimport { useProfile } from \"../Context/ProfileContext\";\r\nimport { fDateTime } from \"../utils/formatTime\";\r\n\r\nconst AppDepositsTab = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppDepositsTab\")\r\n);\r\n\r\nconst AccountSettings = lazy(() =>\r\n  import(\"../sections/@dashboard/Account/AccountSettings\")\r\n);\r\n\r\nconst reservdlist = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON>\",\r\n    createdAt: \"2024-03-04\",\r\n    tokenId: \"abc123dfsfsdfdssfq\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Jane Smith\",\r\n    createdAt: \"2024-03-03\",\r\n    tokenId: \"def456dfdsfsdfsdfdsf\",\r\n  },\r\n\r\n  // Add more user objects as needed\r\n];\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst invoices = [\r\n  { id: 1, createdAt: \"2023-01-20\", price: 100 },\r\n  { id: 2, createdAt: \"2023-02-19\", price: 150 },\r\n  { id: 3, createdAt: \"2023-03-18\", price: 200 },\r\n  // Add more invoice objects as needed\r\n];\r\nconst fDate = (dateString) => {\r\n  const date = new Date(dateString);\r\n  const options = { day: \"2-digit\", month: \"long\", year: \"numeric\" };\r\n  return date.toLocaleDateString(\"en-US\", options);\r\n};\r\n\r\nconst fCurrency = (amount) => {\r\n  return `$${amount.toFixed(2)}`; // Format currency as needed\r\n};\r\n\r\nexport const SettingsPage = () => {\r\n  const { profile } = useProfile();\r\n  const navigate = useNavigate();\r\n  const [PurchasesPage, setPurchasesPage] = useState(0);\r\n  const [Deposits, setDeposits] = useState([]);\r\n  const [Purchases, setPurchases] = useState([]);\r\n  const [PurchasesRowsPerPage, setPurchasesRowsPerPage] = useState(5);\r\n\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n  };\r\n\r\n  const handlePurchasesPageChange = useCallback((event, value) => {\r\n    setPurchasesPage(value);\r\n  }, []);\r\n\r\n  const handlePurchasesRowsPerPageChange = useCallback((event) => {\r\n    setPurchasesRowsPerPage(event.target.value);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    startTransition(() => {\r\n      fetchDeposits();\r\n    });\r\n    startTransition(() => {\r\n      fetchPurchases();\r\n    });\r\n  }, []);\r\n\r\n  const fetchDeposits = async () => {\r\n    const response = await GetDeposits();\r\n    setDeposits(\r\n      response.data.map((deposit) => {\r\n        deposit.date = fDateTime(deposit.date);\r\n        return deposit;\r\n      })\r\n    );\r\n  };\r\n  const fetchPurchases = async () => {\r\n    const response = await GetPurchases();\r\n    setPurchases(\r\n      response.data.map((purchase) => {\r\n        purchase.date = fDateTime(purchase.date);\r\n        return purchase;\r\n      })\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Container sx>\r\n      <Helmet>\r\n        <title> IDigics | Settings </title>\r\n      </Helmet>\r\n      <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n        Settings\r\n      </Typography>\r\n      <Box>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          aria-label=\"Account tabs\"\r\n        >\r\n          <Tab label=\"Account\" />\r\n          <Tab label=\"Budget\" />\r\n        </Tabs>\r\n      </Box>\r\n\r\n      {activeTab === 0 && (\r\n        <Suspense callback={<CircularProgress />}>\r\n          <AccountSettings />\r\n        </Suspense>\r\n      )}\r\n\r\n      {activeTab === 1 && (\r\n        <Container sx={{ marginTop: \"25px\" }}>\r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={12} md={8}>\r\n              {profile.category && (\r\n                <Card sx={{ p: 3, marginBottom: \"30px\" }}>\r\n                  <Typography\r\n                    variant=\"overline\"\r\n                    sx={{\r\n                      mb: 3,\r\n                      display: \"block\",\r\n                      color: \"text.secondary\",\r\n                    }}\r\n                  >\r\n                    Your Plan\r\n                  </Typography>\r\n                  <Typography variant=\"h4\">{profile.category}</Typography>\r\n                  {profile.category != \"Freelance\" &&\r\n                    profile.category != \"Enterprise\" && (\r\n                      <Box\r\n                        sx={{\r\n                          mt: { xs: 2, sm: 0 },\r\n                          position: {\r\n                            sm: \"absolute\",\r\n                          },\r\n                          top: { sm: 24 },\r\n                          right: { sm: 24 },\r\n                        }}\r\n                      >\r\n                        <Button\r\n                          size=\"small\"\r\n                          variant=\"outlined\"\r\n                          onClick={() => {\r\n                            navigate(\"/admin/bundles\");\r\n                          }}\r\n                        >\r\n                          Upgrade plan\r\n                        </Button>\r\n                      </Box>\r\n                    )}\r\n                </Card>\r\n              )}\r\n              <Card>\r\n                <CardHeader\r\n                  title=\"Deposits\"\r\n                  subheader=\"This table provides a comprehensive history of deposit transactions for tracking purposes.\"\r\n                />\r\n                <CardContent>\r\n                  <Grid item xs={12} md={12}>\r\n                    <Suspense callback={<CircularProgress />}>\r\n                      <AppDepositsTab deposits={Deposits} />\r\n                    </Suspense>\r\n                  </Grid>\r\n                </CardContent>\r\n              </Card>\r\n              <Card sx={{ marginTop: \"30px\" }}>\r\n                <CardHeader\r\n                  title=\"Purchases\"\r\n                  subheader=\"This table serves as a record of past purchases, allowing you to track your purchases history.\"\r\n                />\r\n                <CardContent>\r\n                  <Grid item xs={12} md={12}>\r\n                    <Suspense callback={<CircularProgress />}>\r\n                      <AppPurchasesTab\r\n                        purchases={Purchases}\r\n                        onPageChange={handlePurchasesPageChange}\r\n                        onRowsPerPageChange={handlePurchasesRowsPerPageChange}\r\n                        page={PurchasesPage}\r\n                        rowsPerPage={PurchasesRowsPerPage}\r\n                      />\r\n                    </Suspense>\r\n                  </Grid>\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={12} md={4}>\r\n              {/* <Card>\r\n                                <CardContent>\r\n                                    <Stack spacing={3} alignItems=\"flex-end\">\r\n                                        <Typography\r\n                                            variant=\"subtitle1\"\r\n                                            sx={{ width: 1 }}\r\n                                        >\r\n                                            Invoice History\r\n                                        </Typography>\r\n\r\n                                        <Stack spacing={2} sx={{ width: 1 }}>\r\n                                            {invoices.map((invoice) => (\r\n                                                <Stack\r\n                                                    key={invoice.id}\r\n                                                    direction=\"row\"\r\n                                                    justifyContent=\"space-between\"\r\n                                                    sx={{ width: 1 }}\r\n                                                >\r\n                                                    <Typography\r\n                                                        variant=\"body2\"\r\n                                                        sx={{ minWidth: 160 }}\r\n                                                    >\r\n                                                        {fDate(\r\n                                                            invoice.createdAt\r\n                                                        )}\r\n                                                    </Typography>\r\n                                                    <Typography variant=\"body2\">\r\n                                                        {fCurrency(\r\n                                                            invoice.price\r\n                                                        )}\r\n                                                    </Typography>\r\n                                                    <RouterLink\r\n                                                        to=\"#\"\r\n                                                        style={{\r\n                                                            textDecoration:\r\n                                                                \"none\",\r\n                                                            color: \"#ff715b\",\r\n                                                        }}\r\n                                                    >\r\n                                                        PDF\r\n                                                    </RouterLink>\r\n                                                </Stack>\r\n                                            ))}\r\n                                        </Stack>\r\n\r\n                                        <Button\r\n                                            size=\"small\"\r\n                                            endIcon={<ArrowForwardIcon />}\r\n                                        >\r\n                                            All invoices\r\n                                        </Button>\r\n                                    </Stack>\r\n                                </CardContent>\r\n                            </Card> */}\r\n            </Grid>\r\n          </Grid>\r\n        </Container>\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SettingsPage;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\TrackPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\RatingPage.js", ["634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\profileUser.js", ["650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SearchResults.js", ["662", "663"], [], "import { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Container,\r\n  Rating,\r\n  Stack,\r\n} from \"@mui/material\";\r\nimport LocationOnIcon from \"@mui/icons-material/LocationOn\";\r\nimport WorkOutlineIcon from \"@mui/icons-material/WorkOutline\";\r\nimport StarBorderIcon from \"@mui/icons-material/StarBorder\";\r\nimport Avatar from \"@mui/material/Avatar\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useSearch } from \"../Context/SearchContext\";\r\nimport SearchNotFound from \"./SearchNotFound\";\r\nimport { motion } from \"framer-motion\";\r\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\r\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\r\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { CircularProgress } from \"@mui/material\";\r\n\r\nconst SearchResults = () => {\r\n  const { searchResults, fetchSearchResults, loading } = useSearch();\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [displayCount, setDisplayCount] = useState(5);\r\n  const [sortedResults, setSortedResults] = useState([]);\r\n  const [searchParams] = useSearchParams();\r\n\r\n  // Fetch search results only when search parameters change\r\n  useEffect(() => {\r\n    const handleSearch = async () => {\r\n      if (searchParams && searchParams.toString() !== \"\") {\r\n        await fetchSearchResults(searchParams.get(\"q\"));\r\n      }\r\n    };\r\n    handleSearch(); // Call search function only when searchParams change\r\n  }, [searchParams]);\r\n\r\n  // Update sortedResults whenever searchResults or activeTab changes\r\n  useEffect(() => {\r\n    setSortedResults(sortResultsByTab(activeTab));\r\n  }, [searchResults, activeTab]);\r\n\r\n  const GetCategoryIcon = (category) => {\r\n    switch (category) {\r\n      case \"Free\":\r\n        return <EmojiPeopleIcon fontSize=\"Large\" />;\r\n      case \"Student\":\r\n        return <BusinessCenterIcon fontSize=\"Large\" />;\r\n      case \"Freelance\":\r\n        return <EngineeringIcon fontSize=\"Large\" />;\r\n      case \"Entrepreneur\":\r\n        return <ApartmentIcon fontSize=\"Large\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    setDisplayCount(5); // Reset display count when switching tabs\r\n  };\r\n\r\n  const sortResultsByTab = (tabValue) => {\r\n    switch (tabValue) {\r\n      case 1:\r\n        return (\r\n          searchResults\r\n            // .filter(\r\n            //     (result) => !result.user.rate || result.user.rate === 0\r\n            // )\r\n            .sort((a, b) => a.userName.localeCompare(b.userName))\r\n        );\r\n      case 0:\r\n        return searchResults\r\n          .filter((result) => result.user.rate > 0)\r\n          .sort((a, b) => b.user.rate - a.user.rate);\r\n      default:\r\n        return searchResults;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Typography variant=\"h5\" component=\"h1\" gutterBottom>\r\n        Search\r\n      </Typography>\r\n      <Typography component=\"h2\" color=\"textSecondary\">\r\n        Results for {searchParams.get(\"q\")}\r\n      </Typography>\r\n      {loading ? (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"300px\",\r\n            flexDirection: \"column\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <CircularProgress size={60} />\r\n          <Typography variant=\"h6\" color=\"textSecondary\">\r\n            Searching...\r\n          </Typography>\r\n        </Box>\r\n      ) : searchResults.length > 0 ? (\r\n        <Box sx={{ mt: 5 }}>\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              gap: \"10px\",\r\n              padding: \"10px\",\r\n              marginBottom: \"30px\",\r\n            }}\r\n          >\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"Account tabs\"\r\n            >\r\n              <Tab label=\"Rate\" />\r\n              <Tab label=\"All\" />\r\n            </Tabs>\r\n          </Box>\r\n          <Grid container spacing={3}>\r\n            {sortedResults.slice(0, displayCount).map((result) => (\r\n              <Grid key={result.id} item xs={12}>\r\n                <motion.div\r\n                  className=\"animate-on-scroll\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{\r\n                    duration: 0.5,\r\n                    ease: \"easeOut\",\r\n                  }}\r\n                >\r\n                  <Link\r\n                    to={`/Profile/${result.userName}`}\r\n                    target=\"_blank\"\r\n                    rel=\"noreferrer\"\r\n                    style={{\r\n                      textDecoration: \"none\",\r\n                      color: \"inherit\",\r\n                    }}\r\n                  >\r\n                    <Card\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        flexDirection: \"row\",\r\n                        alignItems: \"center\",\r\n                        p: 3,\r\n                        gap: \"10px\",\r\n                        boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.1)\",\r\n                        transition: \"box-shadow 0.3s ease\",\r\n                        \"&:hover\": {\r\n                          boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\r\n                        },\r\n                      }}\r\n                    >\r\n                      <Stack\r\n                        direction=\"row\"\r\n                        alignItems=\"center\"\r\n                        spacing={2}\r\n                        sx={{ flex: 1 }}\r\n                      >\r\n                        <Avatar\r\n                          alt={\r\n                            result.user.firstName + \" \" + result.user.lastName\r\n                          }\r\n                          src={result.profilePicture}\r\n                          sx={{\r\n                            width: 80,\r\n                            height: 80,\r\n                          }}\r\n                        />\r\n                        <Stack direction=\"column\" spacing={1}>\r\n                          <Typography variant=\"h6\">\r\n                            {result.user.firstName} {result.user.lastName}{\" \"}\r\n                            <Stack\r\n                              direction=\"row\"\r\n                              alignItems=\"baseline\"\r\n                              spacing={1}\r\n                              sx={{\r\n                                display: \"inline-flex\",\r\n                                marginLeft: 1,\r\n                              }}\r\n                            >\r\n                              <Typography\r\n                                variant=\"caption\"\r\n                                sx={{\r\n                                  color: \"rgba(20, 43, 58, 0.5)\",\r\n                                }}\r\n                              >\r\n                                @{result.userName}\r\n                              </Typography>\r\n                              <Typography\r\n                                variant=\"body2\"\r\n                                sx={{\r\n                                  color: \"rgba(20, 43, 58, 0.5)\",\r\n                                }}\r\n                              >\r\n                                {GetCategoryIcon(result.user.category)}\r\n                              </Typography>\r\n                            </Stack>\r\n                          </Typography>\r\n                          {result.user.rate > 0 && (\r\n                            <Rating\r\n                              name=\"read-only\"\r\n                              value={result.user.rate}\r\n                              readOnly\r\n                              emptyIcon={<StarBorderIcon fontSize=\"inherit\" />}\r\n                              sx={{\r\n                                marginLeft: \"10px\",\r\n                                fontSize: \"13px\",\r\n                              }}\r\n                            />\r\n                          )}\r\n                          <Box\r\n                            sx={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              gap: \"10px\",\r\n                            }}\r\n                          >\r\n                            {result.country && (\r\n                              <>\r\n                                <LocationOnIcon\r\n                                  sx={{\r\n                                    fontSize: \"15px\",\r\n                                  }}\r\n                                />\r\n                                <Typography\r\n                                  variant=\"caption\"\r\n                                  sx={{\r\n                                    color: \"rgba(20, 43, 58, 0.5)\",\r\n                                  }}\r\n                                >\r\n                                  {result.country}\r\n                                </Typography>\r\n                              </>\r\n                            )}\r\n                          </Box>\r\n                          <Box\r\n                            sx={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              gap: \"10px\",\r\n                            }}\r\n                          >\r\n                            {result.occupation && (\r\n                              <>\r\n                                <WorkOutlineIcon\r\n                                  sx={{\r\n                                    fontSize: \"15px\",\r\n                                  }}\r\n                                />\r\n                                <Typography\r\n                                  variant=\"caption\"\r\n                                  sx={{\r\n                                    color: \"rgba(20, 43, 58, 0.5)\",\r\n                                  }}\r\n                                >\r\n                                  {result.occupation}\r\n                                </Typography>\r\n                              </>\r\n                            )}\r\n                          </Box>\r\n                        </Stack>\r\n                      </Stack>\r\n                    </Card>\r\n                  </Link>\r\n                </motion.div>\r\n              </Grid>\r\n            ))}\r\n          </Grid>\r\n          {displayCount < sortedResults.length && (\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                mt: 3,\r\n              }}\r\n            >\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={() => setDisplayCount(displayCount + 5)}\r\n              >\r\n                View More\r\n              </Button>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      ) : (\r\n        <SearchNotFound searchQuery={searchParams.get(\"q\")} />\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SearchResults;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Profile.js", ["664", "665", "666", "667", "668", "669"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\AppManageCoupons.js", ["670"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\globalStyles.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\customShadows.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\palette.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\shadows.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\typography.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\styles.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\chart\\useChart.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\scroll-to-top\\ScrollToTop.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Api.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\Context\\config.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\AnalyticsData.ts", ["671"], [], "import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostViewData {\r\n  userId: number;\r\n  country?: string;\r\n  gender?: string;\r\n  date?: string;\r\n}\r\n\r\nexport interface PostClickData {\r\n  userId: number;\r\n  linkId: number;\r\n  gender?: string;\r\n  country?: string;\r\n  category: string;\r\n  date?: string;\r\n}\r\n\r\nexport const GetClicks = async () => {\r\n  try {\r\n    const authToken = getAuthToken();\r\n\r\n    const response = await api.get(`${BASE_URL}/Analytics/GetClicks`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const GetViews = async () => {\r\n  try {\r\n    const authToken = getAuthToken();\r\n\r\n    const response = await api.get(`${BASE_URL}/Analytics/GetViews`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const PostClick = async (click: PostClickData) => {\r\n  const Data = {\r\n    userId: click.userId,\r\n    linkId: click.linkId,\r\n    gender: click.gender,\r\n    country: click.country,\r\n    category: click.category,\r\n    date: click.date,\r\n  };\r\n\r\n  try {\r\n    await api.post(`${BASE_URL}/Analytics/CreateClick`, Data, {\r\n      headers: {\r\n        withCredentials: true,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const PostView = async (view: PostViewData) => {\r\n  const Data = {\r\n    userId: view.userId,\r\n    gender: view.gender,\r\n    country: view.country,\r\n    date: view.date,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Analytics/CreateView`, Data, {\r\n      headers: {\r\n        withCredentials: true,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nfunction getAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return cookie.substring(\"authToken=\".length, cookie.length);\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport const calculateDailyViews = (data) => {\r\n  const dailyViews = {};\r\n\r\n  if (data[0].views && data[0].views.length > 0) {\r\n    data[0].views.forEach((view) => {\r\n      const date = new Date(view.date);\r\n\r\n      const formattedDate =\r\n        `${date.getDate().toString().padStart(2, \"0\")}/` +\r\n        `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` +\r\n        `${date.getFullYear()}`;\r\n\r\n      if (dailyViews[formattedDate]) {\r\n        dailyViews[formattedDate] += 1;\r\n      } else {\r\n        dailyViews[formattedDate] = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n  const result = Object.keys(dailyViews).map((date) => ({\r\n    date,\r\n    totalClicks: dailyViews[date],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const mostViewedCountry = (data) => {\r\n  const countryViews = {};\r\n\r\n  if (data && data.length > 0 && data[0].views && data[0].views.length > 0) {\r\n    data[0].views.forEach((countryView) => {\r\n      const country = countryView.country.trim(); // Remove leading/trailing whitespace\r\n\r\n      if (countryViews[country]) {\r\n        countryViews[country] += 1; // Count occurrences\r\n      } else {\r\n        countryViews[country] = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n  let mostViewedCountry = null;\r\n  let highestViewCount = 0;\r\n\r\n  for (const country in countryViews) {\r\n    if (countryViews[country] > highestViewCount) {\r\n      highestViewCount = countryViews[country];\r\n      mostViewedCountry = country;\r\n    }\r\n  }\r\n\r\n  return mostViewedCountry;\r\n};\r\n\r\nexport const calculateDailyClicks = (data) => {\r\n  const dailyClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        const date = new Date(click.date);\r\n\r\n        const formattedDate =\r\n          `${date.getDate().toString().padStart(2, \"0\")}/` +\r\n          `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` +\r\n          `${date.getFullYear()}`;\r\n\r\n        if (dailyClicks[formattedDate]) {\r\n          dailyClicks[formattedDate] += 1;\r\n        } else {\r\n          dailyClicks[formattedDate] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(dailyClicks).map((date) => ({\r\n    date,\r\n    totalClicks: dailyClicks[date],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const calculateCountryClicks = (data) => {\r\n  const countryClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        if (countryClicks[click.country]) {\r\n          countryClicks[click.country] += 1;\r\n        } else {\r\n          countryClicks[click.country] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(countryClicks).map((country) => ({\r\n    country,\r\n    totalClicks: countryClicks[country],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const calculateGenderClicks = (data) => {\r\n  const genderClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        if (genderClicks[click.gender]) {\r\n          genderClicks[click.gender] += 1;\r\n        } else {\r\n          genderClicks[click.gender] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(genderClicks).map((gender) => ({\r\n    gender,\r\n    totalClicks: genderClicks[gender],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const MostClickedCountry = (data) => {\r\n  const countryClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.countryClickCounts && item.countryClickCounts.length > 0) {\r\n      item.countryClickCounts.forEach((countryClick) => {\r\n        const country = countryClick.country;\r\n\r\n        if (countryClicks[country]) {\r\n          countryClicks[country] += countryClick.clickCount;\r\n        } else {\r\n          countryClicks[country] = countryClick.clickCount;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  let mostClickedCountry = null;\r\n  let highestClickCount = 0;\r\n\r\n  for (const country in countryClicks) {\r\n    if (countryClicks[country] > highestClickCount) {\r\n      highestClickCount = countryClicks[country];\r\n      mostClickedCountry = country;\r\n    }\r\n  }\r\n\r\n  return mostClickedCountry;\r\n};\r\n\r\nexport const getClickCountsByCategory = (data) => {\r\n  const categories = [\r\n    \"Custom\",\r\n    \"Twitter\",\r\n    \"GitHub\",\r\n    \"Instagram\",\r\n    \"Facebook\",\r\n    \"LinkedIn\",\r\n  ];\r\n\r\n  const clickCountsByCategory = [];\r\n\r\n  categories.forEach((category) => {\r\n    const totalClickCount = data.reduce((total, item) => {\r\n      if (item.linkCategory === category) {\r\n        return total + item.clickCount;\r\n      }\r\n      return total;\r\n    }, 0);\r\n\r\n    clickCountsByCategory.push(totalClickCount);\r\n  });\r\n\r\n  return clickCountsByCategory;\r\n};\r\n\r\nexport const GetCategoryClickCounts = (data) => {\r\n  const categoryClickCounts = {};\r\n\r\n  data.forEach((item) => {\r\n    if (!categoryClickCounts[item.linkCategory])\r\n      categoryClickCounts[item.linkCategory] = 0;\r\n\r\n    categoryClickCounts[item.linkCategory] += item.clickCount;\r\n  });\r\n\r\n  // Convert the accumulated click counts into an array of objects\r\n  const result = Object.keys(categoryClickCounts).map((category) => {\r\n    var icon = category;\r\n    var color;\r\n\r\n    switch (category) {\r\n      case \"Twitter\":\r\n        icon = \"Twitter-circle\";\r\n        color = \"#212121\";\r\n        break;\r\n      case \"Custom\":\r\n        icon = \"Profile\";\r\n        color = \"#007BFF\";\r\n        break;\r\n      case \"Facebook\":\r\n        color = \"#5892d0\";\r\n        break;\r\n      case \"Youtube\":\r\n        color = \"#FF0000\";\r\n        break;\r\n      case \"Instagram\":\r\n        color = \"#D81B60\";\r\n        break;\r\n      case \"Github\":\r\n        color = \"#212121\";\r\n        break;\r\n      case \"LinkedIn\":\r\n        color = \"#00b9f1\";\r\n        break;\r\n      default:\r\n        color = \"#000000\";\r\n        break;\r\n    }\r\n\r\n    return {\r\n      name: category,\r\n      value: categoryClickCounts[category],\r\n      icon: icon.toLowerCase(),\r\n      color: color,\r\n    };\r\n  });\r\n\r\n  return result;\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\PurchasesData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\CouponsData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\ContactData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\LinkData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\Logo.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\SeverErrorIllustration.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\PageNotFoundIllustration.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\BookingCustomerReviews.js", ["672", "673"], [], "import PropTypes from \"prop-types\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { GetCouponsFromCustomers } from \"../../../CouponsData.ts\";\r\nimport Tooltip, { tooltipClasses } from \"@mui/material/Tooltip\";\r\nimport { toast } from \"react-toastify\";\r\n// material\r\nimport { useTheme } from \"@mui/material/styles\";\r\nimport {\r\n    Card,\r\n    Chip,\r\n    Stack,\r\n    Avatar,\r\n    Rating,\r\n    Button,\r\n    CardHeader,\r\n    Typography,\r\n    MobileStepper,\r\n} from \"@mui/material\";\r\n// import SwipeableViews from \"react-swipeable-views\";\r\n// utils\r\nimport { fDateTime } from \"../../../utils/formatTime\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\nconst BootstrapTooltip = styled(({ className, ...props }) => (\r\n    <Tooltip {...props} arrow classes={{ popper: className }} />\r\n))(({ theme }) => ({\r\n    [`& .${tooltipClasses.arrow}`]: {\r\n        color: \"#ee705e\",\r\n    },\r\n    [`& .${tooltipClasses.tooltip}`]: {\r\n        backgroundColor: \"#ee705e\",\r\n    },\r\n}));\r\n// ----------------------------------------------------------------------\r\n\r\nReviewItem.propTypes = {\r\n    item: PropTypes.shape({\r\n        avatar: PropTypes.string,\r\n        description: PropTypes.string,\r\n        name: PropTypes.string,\r\n        postedAt: PropTypes.instanceOf(Date),\r\n        rating: PropTypes.number,\r\n        tags: PropTypes.arrayOf(PropTypes.string),\r\n    }),\r\n};\r\n\r\nfunction ReviewItem({ customer }) {\r\n    const {\r\n        profilePicture,\r\n        firstName,\r\n        lastName,\r\n        skill_QualityOfWork,\r\n        skill_CostEffectiveness,\r\n        skill_Timeliness,\r\n        skill_Communication,\r\n        skill_Agility,\r\n        useDate,\r\n    } = customer;\r\n\r\n    const openNewTab = (url) => {\r\n        window.open(url, \"_blank\");\r\n    };\r\n\r\n    return (\r\n        <Stack spacing={2} sx={{ position: \"relative\", p: 3 }}>\r\n            <Stack\r\n                direction=\"row\"\r\n                alignItems=\"center\"\r\n                spacing={2}\r\n                onClick={() => {\r\n                    openNewTab(`/Profile/${customer.profile.userName}`);\r\n                }}\r\n            >\r\n                <Avatar alt={firstName} src={profilePicture} />\r\n                <div>\r\n                    <Typography variant=\"subtitle2\">\r\n                        {firstName} {lastName}\r\n                    </Typography>\r\n                    <Typography\r\n                        variant=\"caption\"\r\n                        sx={{\r\n                            color: \"text.secondary\",\r\n                            mt: 0.5,\r\n                            display: \"block\",\r\n                        }}\r\n                    >\r\n                        Posted {fDateTime(useDate)}\r\n                    </Typography>\r\n                </div>\r\n            </Stack>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"assess the output/ service and whether it meets your expectations\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Quality of work\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_QualityOfWork}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_QualityOfWork)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"determine if the user provides value for money based on the quality of their work and pricing\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Cost Effectiveness\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_CostEffectiveness}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                        sx={{ display: \"flex\", justifyContent: \"left\" }}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{\r\n                        display: \"flex\",\r\n                        color: \"text.secondary\",\r\n                        justifyContent: \"right\",\r\n                    }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_CostEffectiveness)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"consider if the user delivers work on time or within agreed-upon deadlines\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Timeliness\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Timeliness}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Timeliness)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"evaluate how well the user communicates and understands your requirements\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Communication\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Communication}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Communication)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\"see if it fits working with\"}\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Agility\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Agility}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Agility)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n        </Stack>\r\n    );\r\n}\r\n\r\nexport default function BookingCustomerReviews() {\r\n    const [Customers, setCustomers] = useState([]);\r\n    const theme = useTheme();\r\n    const [activeStep, setActiveStep] = useState(0);\r\n\r\n    useEffect(() => {\r\n        fetchCouponsFromCustomers();\r\n    }, []);\r\n\r\n    const fetchCouponsFromCustomers = async () => {\r\n        try {\r\n            const response = await GetCouponsFromCustomers();\r\n            if (response.error) throw new Error(response.error);\r\n            setCustomers(response.data);\r\n        } catch (error) {\r\n            toast.error(error.message, {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const maxSteps = Customers.length;\r\n\r\n    const handleNext = () => {\r\n        setActiveStep((prevActiveStep) => prevActiveStep + 1);\r\n    };\r\n\r\n    const handleBack = () => {\r\n        setActiveStep((prevActiveStep) => prevActiveStep - 1);\r\n    };\r\n\r\n    return (\r\n        <Card>\r\n            <CardHeader\r\n                title=\"Customer Rates\"\r\n                subheader={`${Customers.length} Rates`}\r\n                sx={{\r\n                    \"& .MuiCardHeader-action\": {\r\n                        alignSelf: \"center\",\r\n                    },\r\n                }}\r\n            />\r\n\r\n            {/* <SwipeableViews\r\n                index={activeStep}\r\n                onChangeIndex={(index) => setActiveStep(index)}\r\n                enableMouseEvents\r\n                axis={theme.direction === \"rtl\" ? \"x-reverse\" : \"x\"}\r\n            >\r\n            {Customers.map((customer, index) => (\r\n                <div key={index}>\r\n                    {Math.abs(activeStep - index) <= 2 ? (\r\n                        <ReviewItem customer={customer} />\r\n                    ) : null}\r\n                </div>\r\n            ))}\r\n        </SwipeableViews> */}\r\n            <MobileStepper\r\n                variant=\"dots\"\r\n                steps={maxSteps}\r\n                position=\"static\"\r\n                activeStep={activeStep}\r\n                nextButton={\r\n                    <Button\r\n                        size=\"small\"\r\n                        onClick={handleNext}\r\n                        disabled={activeStep === maxSteps - 1}\r\n                    >\r\n                        Next\r\n                    </Button>\r\n                }\r\n                backButton={\r\n                    <Button\r\n                        size=\"small\"\r\n                        onClick={handleBack}\r\n                        disabled={activeStep === 0}\r\n                    >\r\n                        Back\r\n                    </Button>\r\n                }\r\n                sx={{ maxWidth: 400, flexGrow: 1, mx: \"auto\" }}\r\n            />\r\n        </Card>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\EmptyContent.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\InviterFriends.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\VerticalLinearStepper.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\hooks\\useResponsive.js", [], ["674"], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\SearchNotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\Appearance.js", ["675", "676", "677", "678", "679", "680", "681", "682", "683"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppTasks.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentVisits.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppWebsiteVisits.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppPurchasesTab.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppConversionRates.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppDepositsTab.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByProfile.js", ["684", "685", "686", "687", "688", "689"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppWidgetSummary.js", ["690"], [], "// @mui\r\nimport PropTypes from \"prop-types\";\r\nimport { alpha, styled } from \"@mui/material/styles\";\r\nimport { Card, Typography } from \"@mui/material\";\r\n// utils\r\nimport { fShortenNumber } from \"../../../utils/formatNumber\";\r\n// components\r\nimport Iconify from \"../../../components/iconify\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst StyledIcon = styled(\"div\")(({ theme }) => ({\r\n    margin: \"auto\",\r\n    display: \"flex\",\r\n    borderRadius: \"50%\",\r\n    alignItems: \"center\",\r\n    width: theme.spacing(8),\r\n    height: theme.spacing(8),\r\n    justifyContent: \"center\",\r\n    marginBottom: theme.spacing(3),\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nAppWidgetSummary.propTypes = {\r\n    color: PropTypes.string,\r\n    icon: PropTypes.string,\r\n    title: PropTypes.string.isRequired,\r\n    total: PropTypes.number.isRequired,\r\n    sx: PropTypes.object,\r\n};\r\n\r\nexport default function AppWidgetSummary({\r\n    title,\r\n    total,\r\n    icon,\r\n    color = \"primary\",\r\n    sx,\r\n    ...other\r\n}) {\r\n    return (\r\n        <Card\r\n            sx={{\r\n                py: 5,\r\n                boxShadow: 0,\r\n                textAlign: \"center\",\r\n                color: (theme) => theme.palette[color].darker,\r\n                bgcolor: (theme) => theme.palette[color].lighter,\r\n                ...sx,\r\n            }}\r\n            {...other}\r\n        >\r\n            <StyledIcon\r\n                sx={{\r\n                    color: (theme) => theme.palette[color].dark,\r\n                    backgroundImage: (theme) =>\r\n                        `linear-gradient(135deg, ${alpha(\r\n                            theme.palette[color].dark,\r\n                            0\r\n                        )} 0%, ${alpha(theme.palette[color].dark, 0.24)} 100%)`,\r\n                }}\r\n            >\r\n                <Iconify icon={icon} width={24} height={24} />\r\n            </StyledIcon>\r\n\r\n            <Typography variant=\"h3\">{total}</Typography>\r\n\r\n            <Typography variant=\"subtitle2\" sx={{ opacity: 0.72 }}>\r\n                {title}\r\n            </Typography>\r\n        </Card>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReviewTrack.js", ["691", "692", "693", "694"], [], "import { styled } from \"@mui/system\";\r\nimport { formatFloatToOneDecimal } from \"../../../utils/formatNumber\";\r\nimport { keyframes } from \"@emotion/react\";\r\nimport {\r\n    Grid,\r\n    Rating,\r\n    Box,\r\n    Typography,\r\n    LinearProgress,\r\n    Stack,\r\n} from \"@mui/material\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst RatingStyle = styled(Rating)(({ theme }) => ({\r\n    marginBottom: \"5vh\",\r\n    fontSize: \"calc(8vh + 1rem)\",\r\n}));\r\n\r\nconst GridStyle = styled(Grid)(({ theme }) => ({\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    flexDirection: \"column\",\r\n    justifyContent: \"center\",\r\n}));\r\n\r\nconst fadeInAnimation = keyframes`\r\n  0% {\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n`;\r\n\r\nconst bounceAnimation = keyframes`\r\n  0% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n`;\r\n\r\nconst rotateAnimation = keyframes`\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n`;\r\n\r\nconst pulsateAnimation = keyframes`\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.04);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n`;\r\n\r\nconst glowAnimation = keyframes`\r\n  0% {\r\n    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.5), 0 0 15px rgba(255, 0, 0, 0.5);\r\n  }\r\n  50% {\r\n    text-shadow: 0 0 10px rgba(255, 0, 0, 0.8), 0 0 15px rgba(255, 0, 0, 0.8), 0 0 20px rgba(255, 0, 0, 0.8);\r\n  }\r\n  100% {\r\n    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.5), 0 0 15px rgba(255, 0, 0, 0.5);\r\n  }\r\n`;\r\n\r\nfunction ProgressItem({ star }) {\r\n    const { name, starCount } = star;\r\n    return (\r\n        <Stack direction=\"row\" alignItems=\"center\" spacing=\"3vh\">\r\n            <Typography\r\n                variant=\"subtitle2\"\r\n                sx={{\r\n                    display: \"flex\",\r\n                    width: \"35%\",\r\n                    justifyContent: \"left\",\r\n                    fontSize: \"2.8vh\",\r\n                    fontWeight: \"Bold\",\r\n                }}\r\n            >\r\n                {name}\r\n            </Typography>\r\n            <LinearProgress\r\n                variant=\"determinate\"\r\n                value={(starCount / 5) * 100}\r\n                sx={{\r\n                    flexGrow: 1,\r\n                    bgcolor: \"divider\",\r\n                    height: \"1.5vh\",\r\n                }}\r\n            />\r\n            <Typography\r\n                variant=\"subtitle3\"\r\n                sx={{\r\n                    color: \"text.secondary\",\r\n                    minWidth: \"40px\",\r\n                    fontSize: \"3vh\",\r\n                }}\r\n            >\r\n                {formatFloatToOneDecimal(starCount)}\r\n            </Typography>\r\n        </Stack>\r\n    );\r\n}\r\n\r\nconst RatingDetailsReview = ({ product }) => {\r\n    const { totalRating, Ratings, rateCount } = product;\r\n    return (\r\n        <Box>\r\n            <Grid container>\r\n                <GridStyle item xs={6} md={6} lg={6}>\r\n                    <Typography\r\n                        sx={{\r\n                            color: \"error.main\",\r\n                            fontSize: \"27vh\",\r\n                            fontWeight: \"bold\",\r\n                            animation: `${pulsateAnimation} 4s infinite`,\r\n                        }}\r\n                    >\r\n                        {formatFloatToOneDecimal(totalRating)}/5\r\n                    </Typography>\r\n                    <RatingStyle readOnly value={totalRating} precision={0.1} />\r\n                    <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                            color: \"text.secondary\",\r\n                            fontSize: \"4vh\",\r\n                        }}\r\n                    >\r\n                        ({rateCount}&nbsp;reviews)\r\n                    </Typography>\r\n                </GridStyle>\r\n\r\n                <GridStyle item xs={6} md={6} lg={6} marginTop=\"7vh\">\r\n                    <Stack spacing=\"6vh\" sx={{ width: \"90%\" }}>\r\n                        {Ratings.map((rating) => (\r\n                            <ProgressItem key={rating.name} star={rating} />\r\n                        ))}\r\n                    </Stack>\r\n                </GridStyle>\r\n            </Grid>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default RatingDetailsReview;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Coupons\\CheckoutReserved.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\formatTime.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\pages\\RatingDialog.js", ["695"], [], "import React, { useState } from \"react\";\r\nimport {\r\n    <PERSON><PERSON>,\r\n    <PERSON>alog<PERSON><PERSON>nt,\r\n    Button,\r\n    Box,\r\n    Paper,\r\n    <PERSON>per,\r\n    StepLabel,\r\n    Typography,\r\n    Step,\r\n    StepContent,\r\n    TextField,\r\n    Grid,\r\n    IconButton,\r\n} from \"@mui/material\";\r\nimport ThankYouCard from \"../sections/@dashboard/Rating/ThankYouCard\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n\r\nconst Steps = [\r\n    {\r\n        label: \"Provide the coupon please\",\r\n        description:\r\n            \"Can you please provide the coupon serial key to perform this rating\",\r\n    },\r\n    {\r\n        label: \"Quality of work\",\r\n        description:\r\n            \"Evaluate the overall quality and accuracy of the work performed by the individual.\",\r\n    },\r\n    {\r\n        label: \"Communication\",\r\n        description:\r\n            \"Assess the effectiveness of communication between you and the individual.\",\r\n    },\r\n    {\r\n        label: \"Timeliness\",\r\n        description:\r\n            \"Consider the punctuality and adherence to deadlines in completing tasks.\",\r\n    },\r\n    {\r\n        label: \"Cost-Effectiveness\",\r\n        description:\r\n            \"Review the efficiency and value for money of the services provided.\",\r\n    },\r\n    {\r\n        label: \"Agility\",\r\n        description:\r\n            \"Examine whether the individual conducts themselves with integrity and adherence to ethical principles.\",\r\n    },\r\n];\r\n\r\nexport default function RatingDialog({\r\n    serialKey,\r\n    openDialog,\r\n    onClose,\r\n    onClick,\r\n}) {\r\n    const [feedbackScores, setFeedbackScores] = useState(\r\n        Array(Steps.length).fill(0)\r\n    );\r\n    const [Skill_QualityOfWork, setSkill_QualityOfWork] = useState(0);\r\n    const [Skill_CostEffectiveness, setSkill_CostEffectiveness] = useState(0);\r\n    const [Skill_Timeliness, setSkill_Timeliness] = useState(0);\r\n    const [Skill_Communication, setSkill_Communication] = useState(0);\r\n    const [Skill_Agility, setSkill_Agility] = useState(0);\r\n\r\n    const [SerialKey, setSerialKey] = useState(serialKey ? serialKey : \"\");\r\n\r\n    const handleRatingClick = (score, step) => {\r\n        const updatedFeedbackScores = [...feedbackScores];\r\n        updatedFeedbackScores[step] = score;\r\n        setFeedbackScores(updatedFeedbackScores);\r\n\r\n        switch (step) {\r\n            case 1:\r\n                setSkill_QualityOfWork(score);\r\n                break;\r\n            case 2:\r\n                setSkill_CostEffectiveness(score);\r\n                break;\r\n            case 3:\r\n                setSkill_Timeliness(score);\r\n                break;\r\n            case 4:\r\n                setSkill_Communication(score);\r\n                break;\r\n            case 5:\r\n                setSkill_Agility(score);\r\n                break;\r\n        }\r\n    };\r\n\r\n    const [activeStep, setActiveStep] = useState(SerialKey ? 1 : 0);\r\n\r\n    const handleFinish = async () => {\r\n        onClick({\r\n            SerialKey,\r\n            Skill_QualityOfWork,\r\n            Skill_CostEffectiveness,\r\n            Skill_Timeliness,\r\n            Skill_Communication,\r\n            Skill_Agility,\r\n        });\r\n        onClose();\r\n    };\r\n\r\n    const handleClose = () => {\r\n        onClose();\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Dialog open={openDialog} fullWidth maxWidth=\"sm\">\r\n                <DialogContent>\r\n                    <IconButton\r\n                        onClick={handleClose}\r\n                        sx={{\r\n                            position: \"absolute\",\r\n                            top: 0,\r\n                            right: 0,\r\n                        }}\r\n                    >\r\n                        <CloseIcon />\r\n                    </IconButton>\r\n\r\n                    {activeStep === Steps.length ? (\r\n                        <Paper sx={{ p: 3, mt: 3, bgcolor: \"grey.50012\" }}>\r\n                            <ThankYouCard\r\n                                feedbackScores={[\r\n                                    Skill_QualityOfWork,\r\n                                    Skill_CostEffectiveness,\r\n                                    Skill_Timeliness,\r\n                                    Skill_Communication,\r\n                                    Skill_Agility,\r\n                                ]}\r\n                            />\r\n                            <Button variant=\"contained\" onClick={handleFinish}>\r\n                                Confirm\r\n                            </Button>\r\n                            <Button\r\n                                onClick={() => {\r\n                                    setActiveStep(\r\n                                        (prevActiveStep) => prevActiveStep - 1\r\n                                    );\r\n                                }}\r\n                            >\r\n                                Back\r\n                            </Button>\r\n                        </Paper>\r\n                    ) : (\r\n                        <Stepper activeStep={activeStep} orientation=\"vertical\">\r\n                            {Steps.map((step, index) => (\r\n                                <Step key={step.label}>\r\n                                    <StepLabel\r\n                                        optional={\r\n                                            index === Steps.length - 1 ? (\r\n                                                <Typography variant=\"caption\">\r\n                                                    Last step\r\n                                                </Typography>\r\n                                            ) : null\r\n                                        }\r\n                                    >\r\n                                        {step.label}\r\n                                    </StepLabel>\r\n                                    <StepContent>\r\n                                        {index === 0 ? (\r\n                                            <Grid\r\n                                                container\r\n                                                spacing={2}\r\n                                                alignItems=\"center\"\r\n                                            >\r\n                                                <Grid item xs={12} sm={6}>\r\n                                                    <TextField\r\n                                                        label=\"Coupon Serial Key\"\r\n                                                        value={SerialKey}\r\n                                                        onChange={(event) =>\r\n                                                            setSerialKey(\r\n                                                                event.target\r\n                                                                    .value\r\n                                                            )\r\n                                                        }\r\n                                                        fullWidth\r\n                                                    />\r\n                                                </Grid>\r\n                                                <Grid\r\n                                                    item\r\n                                                    xs={12}\r\n                                                    sm={6}\r\n                                                    container\r\n                                                    justifyContent=\"flex-end\"\r\n                                                >\r\n                                                    <Button\r\n                                                        variant=\"contained\"\r\n                                                        color=\"primary\"\r\n                                                        onClick={() =>\r\n                                                            setActiveStep(\r\n                                                                (\r\n                                                                    prevActiveStep\r\n                                                                ) =>\r\n                                                                    prevActiveStep +\r\n                                                                    1\r\n                                                            )\r\n                                                        }\r\n                                                        disabled={\r\n                                                            SerialKey === \"\"\r\n                                                        }\r\n                                                    >\r\n                                                        Next\r\n                                                    </Button>\r\n                                                </Grid>\r\n                                            </Grid>\r\n                                        ) : (\r\n                                            <>\r\n                                                <Typography>\r\n                                                    {step.description}\r\n                                                </Typography>\r\n                                                <Box sx={{ mt: 3 }}>\r\n                                                    <Grid container spacing={2}>\r\n                                                        <Grid item xs={12}>\r\n                                                            <Grid\r\n                                                                container\r\n                                                                justifyContent=\"center\"\r\n                                                                spacing={2}\r\n                                                            >\r\n                                                                {[\r\n                                                                    1, 2, 3, 4,\r\n                                                                    5,\r\n                                                                ].map(\r\n                                                                    (score) => (\r\n                                                                        <Grid\r\n                                                                            item\r\n                                                                            key={\r\n                                                                                score\r\n                                                                            }\r\n                                                                        >\r\n                                                                            <Button\r\n                                                                                onClick={() => {\r\n                                                                                    handleRatingClick(\r\n                                                                                        score,\r\n                                                                                        activeStep\r\n                                                                                    );\r\n                                                                                    setActiveStep(\r\n                                                                                        (\r\n                                                                                            prevActiveStep\r\n                                                                                        ) =>\r\n                                                                                            prevActiveStep +\r\n                                                                                            1\r\n                                                                                    );\r\n                                                                                }}\r\n                                                                                sx={{\r\n                                                                                    bgcolor:\r\n                                                                                        feedbackScores[\r\n                                                                                            activeStep\r\n                                                                                        ] ===\r\n                                                                                        score\r\n                                                                                            ? \"primary.main\"\r\n                                                                                            : \"grey.300\",\r\n                                                                                    color:\r\n                                                                                        feedbackScores[\r\n                                                                                            activeStep\r\n                                                                                        ] ===\r\n                                                                                        score\r\n                                                                                            ? \"common.white\"\r\n                                                                                            : \"grey.800\",\r\n                                                                                    \"&:hover\":\r\n                                                                                        {\r\n                                                                                            bgcolor:\r\n                                                                                                \"primary.dark\",\r\n                                                                                            color: \"common.white\",\r\n                                                                                        },\r\n                                                                                    borderRadius:\r\n                                                                                        \"50%\",\r\n                                                                                    minWidth:\r\n                                                                                        \"50px\",\r\n                                                                                    height: \"50px\",\r\n                                                                                    fontSize:\r\n                                                                                        \"18px\",\r\n                                                                                    fontWeight: 600,\r\n                                                                                }}\r\n                                                                            >\r\n                                                                                {\r\n                                                                                    score\r\n                                                                                }\r\n                                                                            </Button>\r\n                                                                        </Grid>\r\n                                                                    )\r\n                                                                )}\r\n                                                            </Grid>\r\n                                                        </Grid>\r\n                                                    </Grid>\r\n                                                    <Button\r\n                                                        disabled={index === 0}\r\n                                                        onClick={() => {\r\n                                                            setActiveStep(\r\n                                                                (\r\n                                                                    prevActiveStep\r\n                                                                ) =>\r\n                                                                    prevActiveStep -\r\n                                                                    1\r\n                                                            );\r\n                                                        }}\r\n                                                    >\r\n                                                        Back\r\n                                                    </Button>\r\n                                                </Box>\r\n                                            </>\r\n                                        )}\r\n                                    </StepContent>\r\n                                </Step>\r\n                            ))}\r\n                        </Stepper>\r\n                    )}\r\n                </DialogContent>\r\n            </Dialog>\r\n        </>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Account\\AccountSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\PhoneLinkDialog.js", ["696", "697", "698", "699", "700", "701"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\cssStyles.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\DashboardLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\WhatsAppLinkDialog.js", ["702", "703", "704", "705", "706", "707"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\PhotoSelector.js", ["708"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Table.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Paper.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Tooltip.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\utils\\formatNumber.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Autocomplete.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Backdrop.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\theme\\overrides\\Typography.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\SquarePhotoSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\iconify\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Rating\\ThankYouCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Contact\\AddCvDialog.js", ["709", "710", "711"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport { Worker, Viewer } from \"@react-pdf-viewer/core\";\r\nimport \"@react-pdf-viewer/core/lib/styles/index.css\";\r\nimport {\r\n    Grid,\r\n    Card,\r\n    CardContent,\r\n    Typography,\r\n    Button,\r\n    Dialog,\r\n    Box,\r\n    DialogContent,\r\n    DialogTitle,\r\n    CircularProgress,\r\n    IconButton,\r\n} from \"@mui/material\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { CreateContact, EditContact } from \"../../../ContactData.ts\";\r\nimport { FileSelector } from \"../../auth/signup/PhotoSelector\";\r\nimport { useProfile } from \"../../../Context/ProfileContext\";\r\nimport PortraitIcon from \"@mui/icons-material/Portrait\";\r\n\r\nconst AddCvDialog = () => {\r\n    const { profile, fetchProfile } = useProfile();\r\n    const [cvContact, setCvContact] = useState(null);\r\n    const [editedContact, setEditedContact] = useState({\r\n        id: 0,\r\n        contactInfo: \"\",\r\n        isPublic: true,\r\n    });\r\n    const [isCvFileFound, setIsCVFileFound] = useState(false);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const fileURLRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const existingCvContact = profile.contacts.find(\r\n            (contact) => contact.category === \"CvFile\"\r\n        );\r\n\r\n        if (existingCvContact) {\r\n            setCvContact(existingCvContact);\r\n            setEditedContact(existingCvContact);\r\n            setIsCVFileFound(true);\r\n            fileURLRef.current = existingCvContact.contactInfo;\r\n        }\r\n        setIsLoading(false);\r\n    }, [profile.contacts]);\r\n\r\n    const handleFileEdit = async (fileDataUrl) => {\r\n        setEditedContact((prevContact) => ({\r\n            ...prevContact,\r\n            contactInfo: fileDataUrl,\r\n        }));\r\n\r\n        if (cvContact) {\r\n            const updatedContact = {\r\n                ...editedContact,\r\n                contactInfo: fileDataUrl,\r\n            };\r\n\r\n            const response = await EditContact(updatedContact);\r\n            if (response) {\r\n                toast.success(\"CV updated successfully\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                });\r\n                fetchProfile();\r\n                fileURLRef.current = fileDataUrl;\r\n            } else {\r\n                toast.error(\"Error updating CV\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                });\r\n            }\r\n        } else {\r\n            const newContact = {\r\n                ContactInfo: fileDataUrl,\r\n                category: \"CvFile\",\r\n                isPublic: true,\r\n                UserId: profile.id,\r\n            };\r\n\r\n            const response = await CreateContact(newContact);\r\n            if (response) {\r\n                toast.success(\"CV added successfully\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                });\r\n                fetchProfile();\r\n                fileURLRef.current = fileDataUrl;\r\n            } else {\r\n                toast.error(\"Error adding CV\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                });\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleDialogOpen = () => {\r\n        setDialogOpen(true);\r\n    };\r\n\r\n    const handleDialogClose = () => {\r\n        setDialogOpen(false);\r\n    };\r\n\r\n    return (\r\n        <Grid item xs={12} md={12}>\r\n            <Card\r\n                sx={{\r\n                    display: \"flex\",\r\n                    flexDirection: \"column\",\r\n                    marginTop: \"20px\",\r\n                }}\r\n            >\r\n                <Box\r\n                    sx={{\r\n                        height: \"30vh\",\r\n                        width: \"100%\",\r\n                        display: { xs: \"none\", sm: \"block\" },\r\n                        overflow: \"hidden\",\r\n                    }}\r\n                >\r\n                    <img\r\n                        src=\"../assets/images/Cv.png\"\r\n                        style={{\r\n                            width: \"100%\",\r\n                            height: \"100%\",\r\n                            objectFit: \"cover\",\r\n                        }}\r\n                    />\r\n                </Box>\r\n                <CardContent\r\n                    sx={{\r\n                        display: \"flex\",\r\n                        flexDirection: \"column\",\r\n                        flexGrow: 1,\r\n                    }}\r\n                >\r\n                    <Typography gutterBottom variant=\"h5\">\r\n                        Boost Your Networking with a Professional CV\r\n                    </Typography>\r\n                    <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\r\n                        Upload your CV and enhance your online presence. Share\r\n                        your experiences, showcase your skills, and maximize\r\n                        opportunities.\r\n                    </Typography>\r\n                    <Typography\r\n                        variant=\"caption\"\r\n                        color=\"textSecondary\"\r\n                        sx={{ marginBottom: \"20px\", display: \"block\" }}\r\n                    >\r\n                        Accepted formats: PDF (Max size: 2MB)\r\n                    </Typography>\r\n                    {/* Push button box to the bottom */}\r\n                    <Box\r\n                        sx={{\r\n                            marginTop: \"auto\", // Push this box to the bottom\r\n                            display: \"flex\",\r\n                            justifyContent: \"space-between\",\r\n                            alignItems: \"center\",\r\n                        }}\r\n                    >\r\n                        <FileSelector onSelect={handleFileEdit} />\r\n                        {isCvFileFound && (\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                color=\"primary\"\r\n                                onClick={() => setDialogOpen(true)}\r\n                                sx={{ borderRadius: \"8px\" }}\r\n                            >\r\n                                <span\r\n                                    style={{\r\n                                        marginRight: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    Show\r\n                                </span>\r\n                                <PortraitIcon />\r\n                            </Button>\r\n                        )}\r\n                    </Box>\r\n                </CardContent>\r\n            </Card>\r\n            {/* Dialog for CV */}\r\n            <Dialog\r\n                open={dialogOpen}\r\n                onClose={() => setDialogOpen(false)}\r\n                fullWidth\r\n                maxWidth=\"md\"\r\n            >\r\n                <DialogTitle>\r\n                    CV Preview <PortraitIcon />\r\n                </DialogTitle>\r\n                <DialogContent>\r\n                    <IconButton\r\n                        sx={{\r\n                            position: \"absolute\",\r\n                            right: 8,\r\n                            top: 8,\r\n                        }}\r\n                        aria-label=\"close\"\r\n                        onClick={() => setDialogOpen(false)}\r\n                    >\r\n                        <CloseIcon />\r\n                    </IconButton>\r\n                    {isLoading ? (\r\n                        <CircularProgress />\r\n                    ) : (\r\n                        <div\r\n                            style={{\r\n                                height: \"600px\",\r\n                                width: \"100%\",\r\n                                overflow: \"auto\",\r\n                            }}\r\n                        >\r\n                            <Worker\r\n                                workerUrl={`https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`}\r\n                            >\r\n                                <Viewer\r\n                                    fileUrl={fileURLRef.current}\r\n                                    showPreviousViewOnLoad={false}\r\n                                />\r\n                            </Worker>\r\n                        </div>\r\n                    )}\r\n                </DialogContent>\r\n            </Dialog>\r\n            <ToastContainer />\r\n        </Grid>\r\n    );\r\n};\r\n\r\nexport default AddCvDialog;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppNewsUpdate.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentSubject.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppTrafficBySite.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByPublicProfile.js", ["712"], [], "import { Box, Paper, Typography, Avatar } from \"@mui/material\";\r\nimport { PostClick } from \"../../../AnalyticsData.ts\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nexport default function AppLinksByPublicProfile({\r\n  AccountId,\r\n  list,\r\n  profile,\r\n  type,\r\n  sameAccount = false,\r\n}) {\r\n  const navigate = useNavigate();\r\n\r\n  const handleClick = async (key, link) => {\r\n    // Add a small delay to ensure touch event is properly registered on iOS\r\n    setTimeout(async () => {\r\n      if (sameAccount === false) {\r\n        await PostClick({\r\n          linkId: key,\r\n          userId: AccountId ?? null,\r\n          gender: profile.profile.gender ?? null,\r\n          country: profile.country ?? null,\r\n          category: link.category,\r\n          date: new Date(),\r\n        });\r\n      }\r\n      if (validateURL(link.body)) {\r\n        // Use a more iOS-friendly approach\r\n        if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n          window.location.href = link.body;\r\n        } else {\r\n          window.open(link.body, \"_blank\");\r\n        }\r\n      } else {\r\n        navigate(\"/404\");\r\n      }\r\n    }, 100); // Small delay to ensure touch event completes\r\n  };\r\n\r\n  const validateURL = (url) => {\r\n    try {\r\n      new URL(url);\r\n      return true;\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      {type == \"socialLinks\"\r\n        ? list.map((site, key) => (\r\n            <Paper\r\n              sx={{\r\n                padding: \"0.8rem 1.5rem\",\r\n                borderRadius: \"10px\",\r\n                background: \"#f5f5f5f5\",\r\n                cursor: \"pointer\",\r\n                display: \"flex\",\r\n                justifyContent: \"center\", // S\r\n                margin: \"0.6rem 0\",\r\n                // border: `2px solid ${site.color}`,\r\n                marginTop: \"15px\",\r\n                boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.50)\",\r\n                transition: \"transform 0.3s ease-in-out\",\r\n                \"&:hover\": {\r\n                  transform: \"scale(1.05)\", // Apply scale transformation on hover\r\n                },\r\n                // iOS touch improvements\r\n                WebkitTapHighlightColor: \"rgba(0, 0, 0, 0.1)\",\r\n                WebkitTouchCallout: \"none\",\r\n                WebkitUserSelect: \"none\",\r\n                userSelect: \"none\",\r\n                touchAction: \"manipulation\",\r\n                minHeight: \"44px\", // Ensure minimum touch target size for iOS\r\n              }}\r\n              key={key}\r\n              onClick={() => handleClick(key, site)}\r\n            >\r\n              <Box color={site.color} fontSize=\"18px\">\r\n                {site.icon}\r\n              </Box>\r\n\r\n              <Typography\r\n                sx={{\r\n                  color: \"#333333\",\r\n                  fontWeight: 1000,\r\n                  margin: \"auto\",\r\n                }}\r\n              >\r\n                {site.name}\r\n              </Typography>\r\n            </Paper>\r\n          ))\r\n        : list.map((site, key) => (\r\n            <Paper\r\n              sx={{\r\n                padding: \"0.8rem 1.5rem\",\r\n                borderRadius: \"10px\",\r\n                position: \"relative\",\r\n                background: \"#f5f5f5f5\",\r\n                cursor: \"pointer\",\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                margin: \"0.6rem 0\",\r\n                marginTop: \"15px\",\r\n                boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.50)\",\r\n                transition: \"transform 0.3s ease-in-out\",\r\n                \"&:hover\": {\r\n                  transform: \"scale(1.05)\",\r\n                },\r\n                \"&::after\": {\r\n                  content: '\"\"',\r\n                  position: \"absolute\",\r\n                  top: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  width: \"10px\",\r\n                  backgroundColor: \"#ff715b\",\r\n                  borderTopRightRadius: \"10px\",\r\n                  borderBottomRightRadius: \"10px\",\r\n                },\r\n                // iOS touch improvements\r\n                WebkitTapHighlightColor: \"rgba(0, 0, 0, 0.1)\",\r\n                WebkitTouchCallout: \"none\",\r\n                WebkitUserSelect: \"none\",\r\n                userSelect: \"none\",\r\n                touchAction: \"manipulation\",\r\n                minHeight: \"44px\", // Ensure minimum touch target size for iOS\r\n              }}\r\n              onClick={() => handleClick(key, site.body)}\r\n              key={key}\r\n            >\r\n              <Avatar\r\n                style={{\r\n                  width: \"40px\",\r\n                  height: \"40px\",\r\n                  borderRadius: \"60%\",\r\n                  border: \"1px solid rgba(10, 20, 20, 0.12)\",\r\n                  marginBottom: \"0.2rem\",\r\n                }}\r\n                src={site.icon}\r\n              />\r\n\r\n              <Typography\r\n                sx={{\r\n                  color: \"#333333\",\r\n                  fontWeight: 1000,\r\n                  fontSize: \"15px\",\r\n                  margin: \"auto\",\r\n                }}\r\n              >\r\n                {site.name}\r\n              </Typography>\r\n            </Paper>\r\n          ))}\r\n    </Box>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppOrderTimeline.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\login\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppBundleWidget.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register1.js", ["713", "714"], [], "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n    Stack,\r\n    FormControl,\r\n    RadioGroup,\r\n    TextField,\r\n    FormControlLabel,\r\n    Radio,\r\n    Dialog,\r\n    DialogActions,\r\n    DialogContent,\r\n    DialogContentText,\r\n    DialogTitle,\r\n    Button,\r\n    Typography,\r\n} from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"./Register1.css\";\r\n\r\nconst Register1 = ({ onNext, data }) => {\r\n    const [formData, setData] = useState(data);\r\n    const [usernameChanged, setUsernameChanged] = useState(false);\r\n    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n    const [isFormValid, setFormValid] = useState(false);\r\n    const [errors, setErrors] = useState({\r\n        firstName: \"\",\r\n        lastName: \"\",\r\n        userName: \"\",\r\n    });\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        validateForm();\r\n    }, [formData]);\r\n\r\n    const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n        if (isFormValid) {\r\n            if (usernameChanged) {\r\n                handleConfirmDialogOpen();\r\n            } else {\r\n                onNext(formData);\r\n            }\r\n        } else {\r\n            toast.error(\r\n                \"Please validate your information and select a gender\",\r\n                {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                }\r\n            );\r\n        }\r\n    };\r\n\r\n    const validateForm = () => {\r\n        const { firstName, lastName, userName, gender } = formData;\r\n\r\n        const isFirstNameValid = /^[A-Za-z ]{3,32}$/.test(firstName);\r\n        const isLastNameValid = /^[A-Za-z ]{3,32}$/.test(lastName);\r\n        const isUserNameValid = /^[A-Za-z0-9_]{3,16}$/.test(userName);\r\n        const isGenderSelected = gender.trim() !== \"\";\r\n\r\n        setErrors({\r\n            firstName:\r\n                firstName.length < 3\r\n                    ? \"First name is required ( minimum 3 characters )\"\r\n                    : isFirstNameValid\r\n                    ? \"\"\r\n                    : \"Invalid first name\",\r\n            lastName:\r\n                lastName.length < 3\r\n                    ? \"Last name is required ( minimum 3 characters )\"\r\n                    : isLastNameValid\r\n                    ? \"\"\r\n                    : \"Invalid last name\",\r\n            userName:\r\n                userName.length < 3\r\n                    ? \"Username is required ( minimum 3 characters )\"\r\n                    : isUserNameValid\r\n                    ? \"\"\r\n                    : \"Username must be 3-16 characters and can only contain letters, numbers, and underscores\",\r\n            gender: isGenderSelected ? \"\" : \"Please select a gender\",\r\n        });\r\n\r\n        setFormValid(\r\n            isFirstNameValid &&\r\n                isLastNameValid &&\r\n                isUserNameValid &&\r\n                isGenderSelected\r\n        );\r\n    };\r\n\r\n    const handleConfirmDialogOpen = () => {\r\n        setConfirmDialogOpen(true);\r\n    };\r\n\r\n    const handleConfirmDialogClose = () => {\r\n        setConfirmDialogOpen(false);\r\n    };\r\n\r\n    const handleUsernameChangeConfirmed = () => {\r\n        setConfirmDialogOpen(false);\r\n        onNext(formData);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <form>\r\n                <Stack spacing={3}>\r\n                    <TextField\r\n                        id=\"firstName\"\r\n                        name=\"firstName\"\r\n                        label=\"First Name\"\r\n                        value={formData.firstName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.firstName && formData.firstName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.firstName}\r\n                    />\r\n\r\n                    <TextField\r\n                        id=\"lastName\"\r\n                        name=\"lastName\"\r\n                        label=\"Last Name\"\r\n                        value={formData.lastName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.lastName && formData.lastName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.lastName}\r\n                    />\r\n\r\n                    <TextField\r\n                        id=\"userName\"\r\n                        name=\"userName\"\r\n                        label=\"Username\"\r\n                        value={formData.userName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.userName && formData.userName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.userName}\r\n                    />\r\n\r\n                    <p style={{ color: \"red\" }}>\r\n                        Note: Usernames are permanent and cannot be changed\r\n                        after registration. Choose wisely!\r\n                    </p>\r\n\r\n                    <FormControl className=\"ms-5 mb-4\">\r\n                        <RadioGroup\r\n                            row\r\n                            aria-labelledby=\"col-sm-3\"\r\n                            name=\"gender\"\r\n                            value={formData.gender}\r\n                            onChange={handleChange}\r\n                        >\r\n                            <FormControlLabel\r\n                                value=\"female\"\r\n                                label={\"Female\"}\r\n                                control={<Radio />}\r\n                            />\r\n                            <FormControlLabel\r\n                                value=\"male\"\r\n                                label={\"Male\"}\r\n                                control={<Radio />}\r\n                            />\r\n                        </RadioGroup>\r\n                    </FormControl>\r\n                </Stack>\r\n\r\n                <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                    Already have an account?{\" \"}\r\n                    <a href=\"/login\" style={{ color: \"silver\" }}>\r\n                        Sign in\r\n                    </a>\r\n                </Typography>\r\n\r\n                <LoadingButton\r\n                    size=\"large\"\r\n                    type=\"submit\"\r\n                    variant=\"contained\"\r\n                    onClick={handleSubmit}\r\n                    disabled={!isFormValid}\r\n                >\r\n                    Next\r\n                </LoadingButton>\r\n\r\n                <ToastContainer />\r\n            </form>\r\n\r\n            {/* Confirmation Dialog */}\r\n            <Dialog\r\n                open={confirmDialogOpen}\r\n                onClose={handleConfirmDialogClose}\r\n                aria-labelledby=\"alert-dialog-title\"\r\n                aria-describedby=\"alert-dialog-description\"\r\n            >\r\n                <DialogTitle id=\"alert-dialog-title\">\r\n                    {\"Confirm Username Change\"}\r\n                </DialogTitle>\r\n                <DialogContent>\r\n                    <DialogContentText id=\"alert-dialog-description\">\r\n                        your username \"{formData.userName}\" is a permanent\r\n                        action. <br /> Are you sure you want to proceed?\r\n                    </DialogContentText>\r\n                </DialogContent>\r\n                <DialogActions>\r\n                    <Button onClick={handleConfirmDialogClose} color=\"primary\">\r\n                        Cancel\r\n                    </Button>\r\n                    <Button\r\n                        onClick={handleUsernameChangeConfirmed}\r\n                        color=\"primary\"\r\n                        autoFocus\r\n                    >\r\n                        Confirm\r\n                    </Button>\r\n                </DialogActions>\r\n            </Dialog>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register1;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\SignUpForm.js", ["715"], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register2.js", ["716"], [], "import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>ack, TextField, Typography } from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\n\r\nconst Register2 = ({ onNext, onPrevious, data }) => {\r\n    const [formData, setFormData] = useState(data);\r\n    const [error, setError] = useState(\"\");\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n        setError(\"\");\r\n    };\r\n\r\n    useEffect(() => {\r\n        validateForm();\r\n    }, [formData]);\r\n\r\n    const handlePrevious = (e) => {\r\n        e.preventDefault();\r\n        onPrevious(formData);\r\n    };\r\n\r\n    const handleNext = (e) => {\r\n        e.preventDefault();\r\n        if (validateForm()) {\r\n            onNext(formData);\r\n        } else {\r\n            toast.error(\"Please validate your information\", {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const validateForm = () => {\r\n        const { contactInfo } = formData;\r\n\r\n        const isPhoneNumberValid =\r\n            /^\\d{8}$/.test(contactInfo) && !/(\\d)\\1{7,}/.test(contactInfo);\r\n\r\n        setError(\r\n            contactInfo.trim() !== \"\"\r\n                ? isPhoneNumberValid\r\n                    ? \"\"\r\n                    : \"Phone number is Invalid\"\r\n                : \"Phone number is required\"\r\n        );\r\n\r\n        return isPhoneNumberValid;\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Stack spacing={3} className=\"mb-5\">\r\n                <TextField\r\n                    id=\"contactInfo\"\r\n                    name=\"contactInfo\"\r\n                    label=\"Phone number\"\r\n                    type=\"tel\"\r\n                    value={formData.contactInfo}\r\n                    onChange={handleChange}\r\n                    error={error && formData.contactInfo.trim() !== \"\"}\r\n                    helperText={error}\r\n                />\r\n            </Stack>\r\n\r\n            <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                Already have an account?{\" \"}\r\n                <a href=\"/login\" style={{ color: \"silver\" }}>\r\n                    Sign in\r\n                </a>\r\n            </Typography>\r\n\r\n            <LoadingButton\r\n                className=\"me-5\"\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handlePrevious}\r\n            >\r\n                Previous\r\n            </LoadingButton>\r\n            <LoadingButton\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handleNext}\r\n                disabled={!!error}\r\n            >\r\n                Next\r\n            </LoadingButton>\r\n            <ToastContainer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register2;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\Register3.js", ["717", "718", "719"], [], "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n    Stack,\r\n    IconButton,\r\n    InputAdornment,\r\n    TextField,\r\n    Avatar,\r\n    Typography,\r\n} from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport Iconify from \"../../../components/iconify\";\r\nimport PhotoSelector from \"./PhotoSelector\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport \"./Register3.css\";\r\n\r\nconst Register3 = ({ onNext, onPrevious, data }) => {\r\n    const [formData, setFormData] = useState(data);\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [strength, setStrength] = useState(\"\");\r\n    const [emailError, setEmailError] = useState(\"\");\r\n    const [passwordError, setPasswordError] = useState(\"\");\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n\r\n        if (name === \"password\") validatePassword(value);\r\n    };\r\n\r\n    const handlePhotoSelect = (photoDataUrl) => {\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            profilePicture: photoDataUrl,\r\n        }));\r\n    };\r\n\r\n    const handlePrevious = (e) => {\r\n        e.preventDefault();\r\n        onPrevious(formData);\r\n    };\r\n\r\n    const handleNext = (e) => {\r\n        e.preventDefault();\r\n\r\n        validateEmail(formData.email);\r\n        validatePassword(formData.password);\r\n\r\n        if (!emailError && !passwordError) {\r\n            onNext(formData);\r\n        } else {\r\n            toast.error(`Please validate your information `, {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const validateEmail = (email) => {\r\n        const isValidEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\r\n        setEmailError(isValidEmail ? \"\" : \"Invalid email\");\r\n    };\r\n\r\n    const validatePassword = (password) => {\r\n        let strengthIndicator = -1;\r\n        let upper = false,\r\n            lower = false,\r\n            numbers = false,\r\n            firstname = false,\r\n            lastname = false,\r\n            username = false,\r\n            specialChars = false;\r\n\r\n        if (/[A-Z]/.test(password)) upper = true;\r\n        if (/[a-z]/.test(password)) lower = true;\r\n        if (/\\d/.test(password)) numbers = true;\r\n        if (/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password))\r\n            specialChars = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.firstName.toLowerCase().trim())\r\n        )\r\n            firstname = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.lastName.toLowerCase().trim())\r\n        )\r\n            lastname = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.userName.toLowerCase().trim())\r\n        )\r\n            username = true;\r\n\r\n        if (password.length >= 8) {\r\n            strengthIndicator++;\r\n            if (!firstname && !lastname && !username && password.length > 5)\r\n                strengthIndicator++;\r\n        }\r\n        if (upper && lower && numbers && specialChars) strengthIndicator++;\r\n\r\n        setStrength([\"weak\", \"medium\", \"strong\"][strengthIndicator] || \"\");\r\n\r\n        switch (true) {\r\n            case password.length < 8:\r\n                setPasswordError(\r\n                    \"Password must be at least 8 characters long.\"\r\n                );\r\n                break;\r\n            case firstname || lastname:\r\n                setPasswordError(\r\n                    \"Password must not match your first or last name.\"\r\n                );\r\n                break;\r\n            case username:\r\n                setPasswordError(\r\n                    \"Password must not match your unique user name.\"\r\n                );\r\n                break;\r\n            case !specialChars:\r\n                setPasswordError(\r\n                    \"Password must contain at least one special character.\"\r\n                );\r\n                break;\r\n            case !upper:\r\n                setPasswordError(\r\n                    \"Password must contain at least one upper case character.\"\r\n                );\r\n                break;\r\n            case !lower:\r\n                setPasswordError(\r\n                    \"Password must contain at least one lower case character.\"\r\n                );\r\n                break;\r\n            case !numbers:\r\n                setPasswordError(\"Password must contain numbers.\");\r\n                break;\r\n            default:\r\n                setPasswordError(\"\");\r\n                break;\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Stack spacing={3}>\r\n                <TextField\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    label=\"Email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    error={!!emailError}\r\n                    helperText={emailError}\r\n                    aria-invalid={!!emailError}\r\n                    aria-describedby=\"email-error\"\r\n                />\r\n                <TextField\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    label=\"Password\"\r\n                    value={formData.password}\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    onClick={() =>\r\n                                        setShowPassword(!showPassword)\r\n                                    }\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {showPassword ? (\r\n                                        <Iconify icon=\"eva:eye-fill\" />\r\n                                    ) : (\r\n                                        <Iconify icon=\"eva:eye-off-fill\" />\r\n                                    )}\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                    onChange={handleChange}\r\n                    error={!!passwordError}\r\n                    helperText={passwordError}\r\n                    aria-invalid={!!passwordError}\r\n                    aria-describedby=\"password-error\"\r\n                />\r\n                <div className={`bars ${strength}`}>\r\n                    <div></div>\r\n                </div>\r\n                <div className=\"row mb-5\">\r\n                    <div className=\"col\">\r\n                        <div className=\"photo-selector-label\">\r\n                            Profile Picture\r\n                        </div>\r\n                        <Avatar\r\n                            src={formData.profilePicture}\r\n                            alt=\"User Profile Photo\"\r\n                        />\r\n                        <PhotoSelector onSelect={handlePhotoSelect} />\r\n                    </div>\r\n                </div>\r\n            </Stack>\r\n\r\n            <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                Already have an account?{\" \"}\r\n                <a href=\"/login\" color=\"primary\">\r\n                    Sign in\r\n                </a>\r\n            </Typography>\r\n\r\n            <LoadingButton\r\n                className=\"me-5\"\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handlePrevious}\r\n            >\r\n                Previous\r\n            </LoadingButton>\r\n            <LoadingButton\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handleNext}\r\n                disabled={\r\n                    !!emailError ||\r\n                    !!passwordError ||\r\n                    strength.trim() !== \"strong\"\r\n                }\r\n            >\r\n                Next\r\n            </LoadingButton>\r\n            <ToastContainer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register3;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\app\\AppProfileCard.js", ["720"], [], "import React from \"react\";\r\nimport { Box, Paper, Avatar } from \"@mui/material\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport \"./appprofilecard.css\";\r\n\r\nconst StyledCover = styled(\"img\")({\r\n    top: 0,\r\n    width: \"100%\",\r\n    height: \"100%\",\r\n    objectFit: \"cover\",\r\n    position: \"absolute\",\r\n});\r\n\r\nexport default function AppProfileCard({ User, Profile }) {\r\n    if (!User) {\r\n        return <div>Loading user data...</div>;\r\n    }\r\n\r\n    return (\r\n        <Box\r\n            sx={{\r\n                display: \"grid\",\r\n                gap: 3,\r\n            }}\r\n            className=\"mobile-container\"\r\n        >\r\n            <Paper\r\n                sx={{\r\n                    textAlign: \"center\",\r\n                    fontSize: \"13px\",\r\n                }}\r\n            >\r\n                {/* profile picture */}\r\n                <div\r\n                    className=\"rounded-top text-white\"\r\n                    style={{\r\n                        display: \"flex\",\r\n                        justifyContent: \"center\",\r\n                    }}\r\n                >\r\n                    <Avatar\r\n                        alt=\"Profile\"\r\n                        src={Profile.profilePicture}\r\n                        sx={{\r\n                            marginTop: \"20px\",\r\n                            width: 130,\r\n                            height: 130,\r\n                        }}\r\n                    />\r\n                </div>\r\n                {/* name / occupation / username */}\r\n                <div className=\"p-4\">\r\n                    <div className=\"text-center\">\r\n                        <div>\r\n                            <p className=\"mb profile-name\">\r\n                                {User.firstName} {User.lastName}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                @{Profile.userName}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                {Profile.occupation ? Profile.occupation : \"-\"}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                {Profile.gender ? Profile.gender : \"-\"}\r\n                            </p>\r\n                        </div>\r\n                        {/* <div className=\"px-3\">\r\n                                    <p className=\"mb-1 h6\">\r\n                                        {Profile.occupation\r\n                                            ? Profile.occupation\r\n                                            : \"-\"}\r\n                                    </p>\r\n                                    <p className=\"small text-muted mb-0\">\r\n                                        Occupation\r\n                                    </p>\r\n                                </div>\r\n                                <div className=\"px-3\">\r\n                                    <p className=\"mb-1 h6\">\r\n                                        {Profile.gender ? Profile.gender : \"-\"}\r\n                                    </p>\r\n                                    <p className=\"small text-muted mb-0\">\r\n                                        Gender\r\n                                    </p>\r\n                                </div> */}\r\n                    </div>\r\n                </div>\r\n            </Paper>\r\n        </Box>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\auth\\signup\\RegisterConfirm.js", ["721"], [], "import { React, useEffect } from \"react\";\r\n\r\nconst RegisterConfirm = ({ onSubmit }) => {\r\n    useEffect(() => {\r\n        onSubmit();\r\n    }, []);\r\n    return <></>;\r\n};\r\n\r\nexport default RegisterConfirm;\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\nav\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\iconify\\Iconify.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\Scrollbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\AccountPopover.js", ["722"], [], "import { useState, useEffect } from \"react\";\r\n// @mui\r\nimport {\r\n  Box,\r\n  Divider,\r\n  Typography,\r\n  Stack,\r\n  MenuItem,\r\n  IconButton,\r\n  Popover,\r\n} from \"@mui/material\";\r\n// mocks_\r\nimport { Logout, checkAuthToken } from \"../../../AuthenticationData.ts\";\r\nimport { useProfile } from \"../../../Context/ProfileContext\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nexport default function AccountPopover() {\r\n  const { profile } = useProfile();\r\n  const [Account, setAccount] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n  });\r\n  const [open, setOpen] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  const HandleLogout = async () => {\r\n    try {\r\n      await Logout();\r\n\r\n      let isAuthenticated = checkAuthToken();\r\n\r\n      if (!isAuthenticated) {\r\n        navigate(\"/Login\");\r\n      } else {\r\n        console.error(\"Logout failed: User is still authenticated.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error during logout:\", error);\r\n    }\r\n  };\r\n  const handleOpen = (event) => {\r\n    setOpen(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(null);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProfileData();\r\n  }, [profile]);\r\n\r\n  const fetchProfileData = async () => {\r\n    try {\r\n      setAccount({\r\n        email: profile.email,\r\n        firstName: profile.firstName,\r\n        lastName: profile.lastName,\r\n      });\r\n    } catch (error) {\r\n      if (error.redirectToLogin) {\r\n        navigate(\"/Login\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <IconButton onClick={handleOpen}>\r\n        <i className=\"bi bi-gear-fill\"></i>\r\n      </IconButton>\r\n\r\n      <Popover\r\n        open={Boolean(open)}\r\n        anchorEl={open}\r\n        onClose={handleClose}\r\n        anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\r\n        transformOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n        PaperProps={{\r\n          sx: {\r\n            mt: 1.5,\r\n            ml: 0.75,\r\n            width: 235,\r\n            \"& .MuiMenuItem-root\": {\r\n              typography: \"body2\",\r\n              borderRadius: 0.75,\r\n            },\r\n          },\r\n        }}\r\n      >\r\n        <Box sx={{ my: 1.5, px: 2.5 }}>\r\n          <Typography variant=\"subtitle2\" noWrap>\r\n            {Account.firstName} {Account.lastName}\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ color: \"text.secondary\" }} noWrap>\r\n            {Account.email}\r\n          </Typography>\r\n        </Box>\r\n\r\n        <Divider sx={{ borderStyle: \"dashed\" }} />\r\n\r\n        <Stack sx={{ p: 1 }}>\r\n          {/* <MenuItem onClick={handleClose}>Home</MenuItem> */}\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"User\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Studio\r\n          </MenuItem>\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"ManageCoupons\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Coupons\r\n          </MenuItem>\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"Settings\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Settings\r\n          </MenuItem>\r\n          {/* <MenuItem onClick={handleClose}>Feedback</MenuItem> */}\r\n        </Stack>\r\n\r\n        <Divider sx={{ borderStyle: \"dashed\" }} />\r\n\r\n        <MenuItem onClick={HandleLogout} sx={{ m: 1 }}>\r\n          Logout\r\n        </MenuItem>\r\n      </Popover>\r\n    </>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\Searchbar.js", ["723"], [], "import { useState } from \"react\";\r\n// @mui\r\nimport { styled } from \"@mui/material/styles\";\r\nimport {\r\n    Input,\r\n    Slide,\r\n    Button,\r\n    IconButton,\r\n    InputAdornment,\r\n    ClickAwayListener,\r\n    Link,\r\n} from \"@mui/material\";\r\n// utils\r\nimport { bgBlur } from \"../../../utils/cssStyles\";\r\n// component\r\nimport Iconify from \"../../../components/iconify\";\r\n\r\nimport { toast } from \"react-toastify\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst HEADER_MOBILE = 64;\r\nconst HEADER_DESKTOP = 92;\r\n\r\nconst StyledSearchbar = styled(\"div\")(({ theme }) => ({\r\n    ...bgBlur({ color: theme.palette.background.default }),\r\n    top: 0,\r\n    left: 0,\r\n    zIndex: 99,\r\n    width: \"100%\",\r\n    display: \"flex\",\r\n    position: \"absolute\",\r\n    alignItems: \"center\",\r\n    height: HEADER_MOBILE,\r\n    padding: theme.spacing(0, 3),\r\n    boxShadow: theme.customShadows.z8,\r\n    [theme.breakpoints.up(\"md\")]: {\r\n        height: HEADER_DESKTOP,\r\n        padding: theme.spacing(0, 5),\r\n    },\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function Searchbar() {\r\n    const [open, setOpen] = useState(false);\r\n    const [searchQuery, setSearchQuery] = useState(\"\");\r\n    const navigate = useNavigate();\r\n\r\n    const handleOpen = () => {\r\n        setOpen(!open);\r\n    };\r\n\r\n    const onChange = (event) => {\r\n        setSearchQuery(event.target.value);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setOpen(false);\r\n    };\r\n\r\n    const handleSearchSubmit = async (event) => {\r\n        event.preventDefault();\r\n\r\n        const trimmedQuery = searchQuery.trim();\r\n\r\n        if (!trimmedQuery || /^\\.{3}$/.test(trimmedQuery)) {\r\n            toast.error(\"Please enter a valid search query.\", {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        } else {\r\n            // setSearchParams({ q: trimmedQuery });\r\n            handleClose();\r\n            navigate(`/admin/search?q=${trimmedQuery}`);\r\n        }\r\n    };\r\n\r\n    const handleKeyPress = (event) => {\r\n        if (event.key === \"Enter\") {\r\n            handleSearchSubmit(event);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <ClickAwayListener onClickAway={handleClose}>\r\n            <div>\r\n                {!open && (\r\n                    <IconButton onClick={handleOpen}>\r\n                        <Iconify icon=\"eva:search-fill\" />\r\n                    </IconButton>\r\n                )}\r\n\r\n                <Slide direction=\"down\" in={open} mountOnEnter unmountOnExit>\r\n                    <StyledSearchbar>\r\n                        <Input\r\n                            autoFocus\r\n                            fullWidth\r\n                            disableUnderline\r\n                            placeholder=\"Search…\"\r\n                            startAdornment={\r\n                                <InputAdornment position=\"start\">\r\n                                    <Iconify\r\n                                        icon=\"eva:search-fill\"\r\n                                        sx={{\r\n                                            color: \"text.disabled\",\r\n                                            width: 20,\r\n                                            height: 20,\r\n                                        }}\r\n                                    />\r\n                                </InputAdornment>\r\n                            }\r\n                            sx={{ mr: 1, fontWeight: \"fontWeightBold\" }}\r\n                            onChange={onChange}\r\n                            onKeyPress={handleKeyPress}\r\n                        />\r\n                        <Button\r\n                            variant=\"contained\"\r\n                            onClick={handleSearchSubmit}\r\n                        >\r\n                            Search\r\n                        </Button>\r\n                    </StyledSearchbar>\r\n                </Slide>\r\n            </div>\r\n        </ClickAwayListener>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\layouts\\dashboard\\header\\PalastinePopover.js", ["724"], [], "import { useState } from \"react\";\r\n// @mui\r\nimport { alpha } from \"@mui/material/styles\";\r\nimport { Box, Typography, Stack, IconButton, Popover } from \"@mui/material\";\r\n\r\nimport FamilyRestroomIcon from \"@mui/icons-material/FamilyRestroom\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst LANGS = [\r\n    {\r\n        value: \"en\",\r\n        label: \"English\",\r\n        icon: \"/assets/icons/ic_flag_en.svg\",\r\n    },\r\n];\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function PalastinePopover() {\r\n    const [open, setOpen] = useState(null);\r\n\r\n    const handleOpen = (event) => {\r\n        setOpen(event.currentTarget);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setOpen(null);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <IconButton\r\n                onClick={handleOpen}\r\n                sx={{\r\n                    padding: 0,\r\n                    width: 44,\r\n                    height: 44,\r\n                    ...(open && {\r\n                        bgcolor: (theme) =>\r\n                            alpha(\r\n                                theme.palette.primary.main,\r\n                                theme.palette.action.focusOpacity\r\n                            ),\r\n                    }),\r\n                }}\r\n            >\r\n                <img src=\"/assets/icons/ic_flag_pl.svg\" alt=\"Free Palastine\" />\r\n            </IconButton>\r\n\r\n            <Popover\r\n                open={Boolean(open)}\r\n                anchorEl={open}\r\n                onClose={handleClose}\r\n                anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\r\n                transformOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n                PaperProps={{\r\n                    sx: {\r\n                        p: 1,\r\n                        mt: 1.5,\r\n                        ml: 0.75,\r\n                        width: 300,\r\n                        \"& .MuiMenuItem-root\": {\r\n                            px: 1,\r\n                            typography: \"body2\",\r\n                            borderRadius: 0.75,\r\n                        },\r\n                    },\r\n                }}\r\n            >\r\n                <Stack\r\n                    spacing={2}\r\n                    alignItems=\"center\"\r\n                    sx={{ textAlign: \"center\", p: 2 }}\r\n                >\r\n                    <Box display=\"flex\" justifyContent=\"center\" mt={2}>\r\n                        <Typography variant=\"h6\" marginRight={1}>\r\n                            Free Palestine\r\n                        </Typography>\r\n                        <FamilyRestroomIcon color=\"primary\" fontSize=\"small\" />\r\n                    </Box>\r\n                    <Typography variant=\"body2\">\r\n                        We stand in solidarity with the people of Palestine. Our\r\n                        thoughts and prayers are with them during these\r\n                        difficult times. May peace and justice prevail.\r\n                    </Typography>\r\n                    <Box\r\n                        display=\"flex\"\r\n                        flexWrap=\"wrap\"\r\n                        alignItems=\"center\"\r\n                        justifyContent=\"center\"\r\n                        mt={2}\r\n                        p={1}\r\n                        sx={{ backgroundColor: \"#f0f0f0\", borderRadius: 1 }}\r\n                    >\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #FreePalestine\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #KeepTalking\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #Gaza\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #SavePalastine\r\n                        </Typography>\r\n                    </Box>\r\n                </Stack>\r\n            </Popover>\r\n        </>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\NavSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\components\\nav-section\\styles.js", [], [], "C:\\Users\\<USER>\\Desktop\\IDigics\\ClientApp\\src\\sections\\@dashboard\\Link\\EmailLinkDialog.js", [], [], {"ruleId": "725", "severity": 1, "message": "726", "line": 14, "column": 8, "nodeType": "727", "endLine": 14, "endColumn": 17, "suggestions": "728"}, {"ruleId": "729", "severity": 1, "message": "730", "line": 1, "column": 54, "nodeType": "731", "messageId": "732", "endLine": 1, "endColumn": 63}, {"ruleId": "733", "severity": 1, "message": "734", "line": 33, "column": 14, "nodeType": "731", "messageId": "735", "endLine": 33, "endColumn": 24}, {"ruleId": "725", "severity": 1, "message": "736", "line": 33, "column": 6, "nodeType": "727", "endLine": 33, "endColumn": 33, "suggestions": "737"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 46, "column": 41, "nodeType": "740", "messageId": "741", "endLine": 46, "endColumn": 42, "suggestions": "742"}, {"ruleId": "725", "severity": 1, "message": "743", "line": 34, "column": 6, "nodeType": "727", "endLine": 34, "endColumn": 8, "suggestions": "744"}, {"ruleId": "729", "severity": 1, "message": "745", "line": 28, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 28, "endColumn": 22}, {"ruleId": "729", "severity": 1, "message": "746", "line": 14, "column": 57, "nodeType": "731", "messageId": "732", "endLine": 14, "endColumn": 60}, {"ruleId": "729", "severity": 1, "message": "747", "line": 15, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 15, "endColumn": 16}, {"ruleId": "729", "severity": 1, "message": "748", "line": 27, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 27, "endColumn": 23}, {"ruleId": "729", "severity": 1, "message": "749", "line": 35, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 35, "endColumn": 15}, {"ruleId": "729", "severity": 1, "message": "750", "line": 40, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 40, "endColumn": 19}, {"ruleId": "729", "severity": 1, "message": "751", "line": 44, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 44, "endColumn": 24}, {"ruleId": "729", "severity": 1, "message": "752", "line": 47, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 47, "endColumn": 25}, {"ruleId": "753", "severity": 1, "message": "754", "line": 98, "column": 25, "nodeType": "755", "messageId": "756", "endLine": 98, "endColumn": 27}, {"ruleId": "753", "severity": 1, "message": "754", "line": 98, "column": 47, "nodeType": "755", "messageId": "756", "endLine": 98, "endColumn": 49}, {"ruleId": "729", "severity": 1, "message": "757", "line": 34, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 34, "endColumn": 18}, {"ruleId": "729", "severity": 1, "message": "758", "line": 53, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 53, "endColumn": 15}, {"ruleId": "729", "severity": 1, "message": "759", "line": 59, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 59, "endColumn": 12}, {"ruleId": "729", "severity": 1, "message": "760", "line": 65, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 65, "endColumn": 16}, {"ruleId": "753", "severity": 1, "message": "761", "line": 161, "column": 37, "nodeType": "755", "messageId": "756", "endLine": 161, "endColumn": 39}, {"ruleId": "753", "severity": 1, "message": "761", "line": 162, "column": 38, "nodeType": "755", "messageId": "756", "endLine": 162, "endColumn": 40}, {"ruleId": "729", "severity": 1, "message": "762", "line": 18, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 18, "endColumn": 10}, {"ruleId": "729", "severity": 1, "message": "763", "line": 19, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 19, "endColumn": 14}, {"ruleId": "729", "severity": 1, "message": "764", "line": 20, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 20, "endColumn": 14}, {"ruleId": "729", "severity": 1, "message": "765", "line": 21, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 21, "endColumn": 19}, {"ruleId": "729", "severity": 1, "message": "766", "line": 22, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 22, "endColumn": 14}, {"ruleId": "729", "severity": 1, "message": "767", "line": 23, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 23, "endColumn": 13}, {"ruleId": "729", "severity": 1, "message": "768", "line": 49, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 49, "endColumn": 17}, {"ruleId": "729", "severity": 1, "message": "769", "line": 68, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 68, "endColumn": 29}, {"ruleId": "729", "severity": 1, "message": "770", "line": 180, "column": 22, "nodeType": "731", "messageId": "732", "endLine": 180, "endColumn": 34}, {"ruleId": "729", "severity": 1, "message": "771", "line": 213, "column": 12, "nodeType": "731", "messageId": "732", "endLine": 213, "endColumn": 31}, {"ruleId": "729", "severity": 1, "message": "772", "line": 214, "column": 12, "nodeType": "731", "messageId": "732", "endLine": 214, "endColumn": 27}, {"ruleId": "729", "severity": 1, "message": "773", "line": 226, "column": 12, "nodeType": "731", "messageId": "732", "endLine": 226, "endColumn": 23}, {"ruleId": "729", "severity": 1, "message": "774", "line": 226, "column": 25, "nodeType": "731", "messageId": "732", "endLine": 226, "endColumn": 39}, {"ruleId": "729", "severity": 1, "message": "775", "line": 227, "column": 12, "nodeType": "731", "messageId": "732", "endLine": 227, "endColumn": 16}, {"ruleId": "729", "severity": 1, "message": "776", "line": 227, "column": 18, "nodeType": "731", "messageId": "732", "endLine": 227, "endColumn": 25}, {"ruleId": "725", "severity": 1, "message": "777", "line": 254, "column": 8, "nodeType": "727", "endLine": 254, "endColumn": 17, "suggestions": "778"}, {"ruleId": "729", "severity": 1, "message": "779", "line": 209, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 209, "endColumn": 24}, {"ruleId": "738", "severity": 1, "message": "739", "line": 544, "column": 52, "nodeType": "740", "messageId": "741", "endLine": 544, "endColumn": 53, "suggestions": "780"}, {"ruleId": "725", "severity": 1, "message": "777", "line": 665, "column": 6, "nodeType": "727", "endLine": 665, "endColumn": 15, "suggestions": "781"}, {"ruleId": "725", "severity": 1, "message": "782", "line": 700, "column": 6, "nodeType": "727", "endLine": 700, "endColumn": 8, "suggestions": "783"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 856, "column": 27, "nodeType": "784", "messageId": "741", "endLine": 856, "endColumn": 28, "suggestions": "785"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 856, "column": 29, "nodeType": "784", "messageId": "741", "endLine": 856, "endColumn": 30, "suggestions": "786"}, {"ruleId": "738", "severity": 1, "message": "787", "line": 856, "column": 37, "nodeType": "784", "messageId": "741", "endLine": 856, "endColumn": 38, "suggestions": "788"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 860, "column": 16, "nodeType": "784", "messageId": "741", "endLine": 860, "endColumn": 17, "suggestions": "789"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 860, "column": 18, "nodeType": "784", "messageId": "741", "endLine": 860, "endColumn": 19, "suggestions": "790"}, {"ruleId": "738", "severity": 1, "message": "787", "line": 860, "column": 26, "nodeType": "784", "messageId": "741", "endLine": 860, "endColumn": 27, "suggestions": "791"}, {"ruleId": "792", "severity": 1, "message": "793", "line": 2285, "column": 25, "nodeType": "794", "endLine": 2285, "endColumn": 468}, {"ruleId": "795", "severity": 1, "message": "796", "line": 2698, "column": 29, "nodeType": "797", "messageId": "756", "endLine": 2701, "endColumn": 37}, {"ruleId": "725", "severity": 1, "message": "798", "line": 44, "column": 6, "nodeType": "727", "endLine": 44, "endColumn": 20, "suggestions": "799"}, {"ruleId": "725", "severity": 1, "message": "800", "line": 49, "column": 6, "nodeType": "727", "endLine": 49, "endColumn": 32, "suggestions": "801"}, {"ruleId": "725", "severity": 1, "message": "802", "line": 174, "column": 6, "nodeType": "727", "endLine": 174, "endColumn": 15, "suggestions": "803"}, {"ruleId": "725", "severity": 1, "message": "726", "line": 211, "column": 6, "nodeType": "727", "endLine": 211, "endColumn": 15, "suggestions": "804"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 447, "column": 9, "nodeType": "807", "messageId": "808", "endLine": 479, "endColumn": 10}, {"ruleId": "738", "severity": 1, "message": "809", "line": 517, "column": 27, "nodeType": "740", "messageId": "741", "endLine": 517, "endColumn": 28, "suggestions": "810"}, {"ruleId": "811", "severity": 1, "message": "812", "line": 706, "column": 23, "nodeType": "794", "endLine": 712, "endColumn": 24}, {"ruleId": "811", "severity": 1, "message": "812", "line": 728, "column": 25, "nodeType": "794", "endLine": 734, "endColumn": 26}, {"ruleId": "729", "severity": 1, "message": "813", "line": 72, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 72, "endColumn": 29}, {"ruleId": "814", "severity": 1, "message": "815", "line": 84, "column": 11, "nodeType": "731", "messageId": "732", "endLine": 84, "endColumn": 19}, {"ruleId": "729", "severity": 1, "message": "816", "line": 10, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 10, "endColumn": 9}, {"ruleId": "729", "severity": 1, "message": "817", "line": 280, "column": 11, "nodeType": "731", "messageId": "732", "endLine": 280, "endColumn": 16}, {"ruleId": "818", "severity": 2, "message": "819", "line": 43, "column": 23, "nodeType": "731", "endLine": 43, "endColumn": 36, "suppressions": "820"}, {"ruleId": "729", "severity": 1, "message": "821", "line": 50, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 50, "endColumn": 13}, {"ruleId": "729", "severity": 1, "message": "822", "line": 119, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 119, "endColumn": 22}, {"ruleId": "753", "severity": 1, "message": "754", "line": 130, "column": 34, "nodeType": "755", "messageId": "756", "endLine": 130, "endColumn": 36}, {"ruleId": "725", "severity": 1, "message": "823", "line": 136, "column": 6, "nodeType": "727", "endLine": 136, "endColumn": 21, "suggestions": "824"}, {"ruleId": "729", "severity": 1, "message": "825", "line": 145, "column": 9, "nodeType": "731", "messageId": "732", "endLine": 145, "endColumn": 33}, {"ruleId": "729", "severity": 1, "message": "826", "line": 158, "column": 9, "nodeType": "731", "messageId": "732", "endLine": 158, "endColumn": 24}, {"ruleId": "738", "severity": 1, "message": "827", "line": 177, "column": 46, "nodeType": "740", "messageId": "741", "endLine": 177, "endColumn": 47, "suggestions": "828"}, {"ruleId": "753", "severity": 1, "message": "754", "line": 791, "column": 22, "nodeType": "755", "messageId": "756", "endLine": 791, "endColumn": 24}, {"ruleId": "792", "severity": 1, "message": "793", "line": 812, "column": 15, "nodeType": "794", "endLine": 812, "endColumn": 132}, {"ruleId": "753", "severity": 1, "message": "754", "line": 27, "column": 20, "nodeType": "755", "messageId": "756", "endLine": 27, "endColumn": 22}, {"ruleId": "753", "severity": 1, "message": "754", "line": 32, "column": 16, "nodeType": "755", "messageId": "756", "endLine": 32, "endColumn": 18}, {"ruleId": "811", "severity": 1, "message": "829", "line": 73, "column": 15, "nodeType": "794", "endLine": 73, "endColumn": 51}, {"ruleId": "811", "severity": 1, "message": "829", "line": 84, "column": 15, "nodeType": "794", "endLine": 84, "endColumn": 52}, {"ruleId": "811", "severity": 1, "message": "829", "line": 213, "column": 15, "nodeType": "794", "endLine": 213, "endColumn": 51}, {"ruleId": "811", "severity": 1, "message": "829", "line": 225, "column": 15, "nodeType": "794", "endLine": 225, "endColumn": 52}, {"ruleId": "729", "severity": 1, "message": "830", "line": 6, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 6, "endColumn": 24}, {"ruleId": "729", "severity": 1, "message": "831", "line": 27, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 27, "endColumn": 22}, {"ruleId": "729", "severity": 1, "message": "832", "line": 36, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 36, "endColumn": 22}, {"ruleId": "729", "severity": 1, "message": "833", "line": 48, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 48, "endColumn": 22}, {"ruleId": "729", "severity": 1, "message": "834", "line": 69, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 69, "endColumn": 20}, {"ruleId": "805", "severity": 1, "message": "806", "line": 75, "column": 9, "nodeType": "807", "messageId": "808", "endLine": 91, "endColumn": 10}, {"ruleId": "729", "severity": 1, "message": "835", "line": 19, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 19, "endColumn": 20}, {"ruleId": "738", "severity": 1, "message": "836", "line": 200, "column": 51, "nodeType": "740", "messageId": "741", "endLine": 200, "endColumn": 52, "suggestions": "837"}, {"ruleId": "738", "severity": 1, "message": "838", "line": 200, "column": 53, "nodeType": "740", "messageId": "741", "endLine": 200, "endColumn": 54, "suggestions": "839"}, {"ruleId": "738", "severity": 1, "message": "787", "line": 200, "column": 55, "nodeType": "740", "messageId": "741", "endLine": 200, "endColumn": 56, "suggestions": "840"}, {"ruleId": "738", "severity": 1, "message": "836", "line": 577, "column": 64, "nodeType": "740", "messageId": "741", "endLine": 577, "endColumn": 65, "suggestions": "841"}, {"ruleId": "738", "severity": 1, "message": "838", "line": 577, "column": 66, "nodeType": "740", "messageId": "741", "endLine": 577, "endColumn": 67, "suggestions": "842"}, {"ruleId": "729", "severity": 1, "message": "835", "line": 20, "column": 10, "nodeType": "731", "messageId": "732", "endLine": 20, "endColumn": 20}, {"ruleId": "738", "severity": 1, "message": "836", "line": 201, "column": 51, "nodeType": "740", "messageId": "741", "endLine": 201, "endColumn": 52, "suggestions": "843"}, {"ruleId": "738", "severity": 1, "message": "838", "line": 201, "column": 53, "nodeType": "740", "messageId": "741", "endLine": 201, "endColumn": 54, "suggestions": "844"}, {"ruleId": "738", "severity": 1, "message": "787", "line": 201, "column": 55, "nodeType": "740", "messageId": "741", "endLine": 201, "endColumn": 56, "suggestions": "845"}, {"ruleId": "738", "severity": 1, "message": "836", "line": 578, "column": 67, "nodeType": "740", "messageId": "741", "endLine": 578, "endColumn": 68, "suggestions": "846"}, {"ruleId": "738", "severity": 1, "message": "838", "line": 578, "column": 69, "nodeType": "740", "messageId": "741", "endLine": 578, "endColumn": 70, "suggestions": "847"}, {"ruleId": "753", "severity": 1, "message": "761", "line": 98, "column": 27, "nodeType": "755", "messageId": "756", "endLine": 98, "endColumn": 29}, {"ruleId": "729", "severity": 1, "message": "848", "line": 102, "column": 11, "nodeType": "731", "messageId": "732", "endLine": 102, "endColumn": 27}, {"ruleId": "729", "severity": 1, "message": "849", "line": 106, "column": 11, "nodeType": "731", "messageId": "732", "endLine": 106, "endColumn": 28}, {"ruleId": "792", "severity": 1, "message": "793", "line": 127, "column": 21, "nodeType": "794", "endLine": 134, "endColumn": 23}, {"ruleId": "753", "severity": 1, "message": "754", "line": 51, "column": 13, "nodeType": "755", "messageId": "756", "endLine": 51, "endColumn": 15}, {"ruleId": "729", "severity": 1, "message": "850", "line": 23, "column": 29, "nodeType": "731", "messageId": "732", "endLine": 23, "endColumn": 47}, {"ruleId": "725", "severity": 1, "message": "823", "line": 42, "column": 8, "nodeType": "727", "endLine": 42, "endColumn": 18, "suggestions": "851"}, {"ruleId": "725", "severity": 1, "message": "823", "line": 72, "column": 6, "nodeType": "727", "endLine": 72, "endColumn": 37, "suggestions": "852"}, {"ruleId": "725", "severity": 1, "message": "823", "line": 21, "column": 8, "nodeType": "727", "endLine": 21, "endColumn": 18, "suggestions": "853"}, {"ruleId": "729", "severity": 1, "message": "730", "line": 1, "column": 27, "nodeType": "731", "messageId": "732", "endLine": 1, "endColumn": 36}, {"ruleId": "738", "severity": 1, "message": "827", "line": 80, "column": 30, "nodeType": "740", "messageId": "741", "endLine": 80, "endColumn": 31, "suggestions": "854"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 80, "column": 47, "nodeType": "740", "messageId": "741", "endLine": 80, "endColumn": 48, "suggestions": "855"}, {"ruleId": "729", "severity": 1, "message": "856", "line": 6, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 6, "endColumn": 18}, {"ruleId": "725", "severity": 1, "message": "857", "line": 6, "column": 8, "nodeType": "727", "endLine": 6, "endColumn": 10, "suggestions": "858"}, {"ruleId": "725", "severity": 1, "message": "802", "line": 52, "column": 6, "nodeType": "727", "endLine": 52, "endColumn": 15, "suggestions": "859"}, {"ruleId": "729", "severity": 1, "message": "860", "line": 11, "column": 5, "nodeType": "731", "messageId": "732", "endLine": 11, "endColumn": 9}, {"ruleId": "729", "severity": 1, "message": "861", "line": 10, "column": 7, "nodeType": "731", "messageId": "732", "endLine": 10, "endColumn": 12}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["862"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "@typescript-eslint/no-redeclare", "'PostSearch' is already defined.", "redeclared", "React Hook useEffect has a missing dependency: 'validatePassword'. Either include it or remove the dependency array.", ["863"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["864", "865"], "React Hook useEffect has a missing dependency: 'verify'. Either include it or remove the dependency array.", ["866"], "'EmojiPeopleIcon' is assigned a value but never used.", "'Box' is defined but never used.", "'motion' is defined but never used.", "'AppWebsiteVisits' is assigned a value but never used.", "'AppTasks' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'CategoryClicks' is assigned a value but never used.", "'LinksTotalCount' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'reservdlist' is assigned a value but never used.", "'invoices' is assigned a value but never used.", "'fDate' is assigned a value but never used.", "'fCurrency' is assigned a value but never used.", "Expected '!==' and instead saw '!='.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'DeleteIcon' is assigned a value but never used.", "'BookingCustomerReviews' is assigned a value but never used.", "'fetchProfile' is assigned a value but never used.", "'showReservedCoupons' is assigned a value but never used.", "'showUsedCoupons' is assigned a value but never used.", "'rowsPerPage' is assigned a value but never used.", "'setRowsPerPage' is assigned a value but never used.", "'page' is assigned a value but never used.", "'setPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["867"], "'CategoryChosen' is assigned a value but never used.", ["868", "869"], ["870"], "React Hook useEffect has missing dependencies: 'fetchUserData' and 'profile'. Either include them or remove the dependency array.", ["871"], "TemplateElement", ["872", "873"], ["874", "875"], "Unnecessary escape character: \\..", ["876", "877"], ["878", "879"], ["880", "881"], ["882", "883"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "no-duplicate-case", "Duplicate case label.", "SwitchCase", "React Hook useEffect has a missing dependency: 'fetchSearchResults'. Either include it or remove the dependency array.", ["884"], "React Hook useEffect has a missing dependency: 'sortResultsByTab'. Either include it or remove the dependency array.", ["885"], "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["886"], ["887"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "Unnecessary escape character: \\-.", ["888", "889"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'CustomerUsedCoupons' is assigned a value but never used.", "@typescript-eslint/no-unused-vars", "'response' is assigned a value but never used.", "'Chip' is defined but never used.", "'theme' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useMediaQuery\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", ["890"], "'themes' is assigned a value but never used.", "'hideBranding' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'validateForm'. Either include it or remove the dependency array.", ["891"], "'handleHideBrandingChange' is assigned a value but never used.", "'handleCardClick' is assigned a value but never used.", "Unnecessary escape character: \\[.", ["892", "893"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'fShortenNumber' is defined but never used.", "'fadeInAnimation' is assigned a value but never used.", "'bounceAnimation' is assigned a value but never used.", "'rotateAnimation' is assigned a value but never used.", "'glowAnimation' is assigned a value but never used.", "'useProfile' is defined but never used.", "Unnecessary escape character: \\(.", ["894", "895"], "Unnecessary escape character: \\).", ["896", "897"], ["898", "899"], ["900", "901"], ["902", "903"], ["904", "905"], ["906", "907"], ["908", "909"], ["910", "911"], ["912", "913"], "'handleDialogOpen' is assigned a value but never used.", "'handleDialogClose' is assigned a value but never used.", "'setUsernameChanged' is assigned a value but never used.", ["914"], ["915"], ["916"], ["917", "918"], ["919", "920"], "'StyledCover' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'onSubmit'. Either include it or remove the dependency array. If 'onSubmit' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["921"], ["922"], "'Link' is defined but never used.", "'LANGS' is assigned a value but never used.", {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"messageId": "927", "fix": "928", "desc": "929"}, {"messageId": "930", "fix": "931", "desc": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"messageId": "927", "fix": "937", "desc": "929"}, {"messageId": "930", "fix": "938", "desc": "932"}, {"desc": "935", "fix": "939"}, {"desc": "935", "fix": "940"}, {"messageId": "927", "fix": "941", "desc": "929"}, {"messageId": "930", "fix": "942", "desc": "932"}, {"messageId": "927", "fix": "943", "desc": "929"}, {"messageId": "930", "fix": "944", "desc": "932"}, {"messageId": "927", "fix": "945", "desc": "929"}, {"messageId": "930", "fix": "946", "desc": "932"}, {"messageId": "927", "fix": "947", "desc": "929"}, {"messageId": "930", "fix": "948", "desc": "932"}, {"messageId": "927", "fix": "949", "desc": "929"}, {"messageId": "930", "fix": "950", "desc": "932"}, {"messageId": "927", "fix": "951", "desc": "929"}, {"messageId": "930", "fix": "952", "desc": "932"}, {"desc": "953", "fix": "954"}, {"desc": "955", "fix": "956"}, {"desc": "957", "fix": "958"}, {"desc": "923", "fix": "959"}, {"messageId": "927", "fix": "960", "desc": "929"}, {"messageId": "930", "fix": "961", "desc": "932"}, {"kind": "962", "justification": "963"}, {"desc": "964", "fix": "965"}, {"messageId": "927", "fix": "966", "desc": "929"}, {"messageId": "930", "fix": "967", "desc": "932"}, {"messageId": "927", "fix": "968", "desc": "929"}, {"messageId": "930", "fix": "969", "desc": "932"}, {"messageId": "927", "fix": "970", "desc": "929"}, {"messageId": "930", "fix": "971", "desc": "932"}, {"messageId": "927", "fix": "972", "desc": "929"}, {"messageId": "930", "fix": "973", "desc": "932"}, {"messageId": "927", "fix": "974", "desc": "929"}, {"messageId": "930", "fix": "975", "desc": "932"}, {"messageId": "927", "fix": "976", "desc": "929"}, {"messageId": "930", "fix": "977", "desc": "932"}, {"messageId": "927", "fix": "978", "desc": "929"}, {"messageId": "930", "fix": "979", "desc": "932"}, {"messageId": "927", "fix": "980", "desc": "929"}, {"messageId": "930", "fix": "981", "desc": "932"}, {"messageId": "927", "fix": "982", "desc": "929"}, {"messageId": "930", "fix": "983", "desc": "932"}, {"messageId": "927", "fix": "984", "desc": "929"}, {"messageId": "930", "fix": "985", "desc": "932"}, {"messageId": "927", "fix": "986", "desc": "929"}, {"messageId": "930", "fix": "987", "desc": "932"}, {"desc": "988", "fix": "989"}, {"desc": "990", "fix": "991"}, {"desc": "988", "fix": "992"}, {"messageId": "927", "fix": "993", "desc": "929"}, {"messageId": "930", "fix": "994", "desc": "932"}, {"messageId": "927", "fix": "995", "desc": "929"}, {"messageId": "930", "fix": "996", "desc": "932"}, {"desc": "997", "fix": "998"}, {"desc": "957", "fix": "999"}, "Update the dependencies array to be: [fetchProfile, profile]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [password, confirmPassword, validatePassword]", {"range": "1002", "text": "1003"}, "removeEscape", {"range": "1004", "text": "963"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1005", "text": "1006"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [verify]", {"range": "1007", "text": "1008"}, "Update the dependencies array to be: [fetchUserData, profile]", {"range": "1009", "text": "1010"}, {"range": "1011", "text": "963"}, {"range": "1012", "text": "1006"}, {"range": "1013", "text": "1010"}, {"range": "1014", "text": "1010"}, {"range": "1015", "text": "963"}, {"range": "1016", "text": "1006"}, {"range": "1017", "text": "963"}, {"range": "1018", "text": "1006"}, {"range": "1019", "text": "963"}, {"range": "1020", "text": "1006"}, {"range": "1021", "text": "963"}, {"range": "1022", "text": "1006"}, {"range": "1023", "text": "963"}, {"range": "1024", "text": "1006"}, {"range": "1025", "text": "963"}, {"range": "1026", "text": "1006"}, "Update the dependencies array to be: [fetchSearchResults, searchParams]", {"range": "1027", "text": "1028"}, "Update the dependencies array to be: [searchResults, activeTab, sortResultsByTab]", {"range": "1029", "text": "1030"}, "Update the dependencies array to be: [fetchProfileData, profile]", {"range": "1031", "text": "1032"}, {"range": "1033", "text": "1001"}, {"range": "1034", "text": "963"}, {"range": "1035", "text": "1006"}, "directive", "", "Update the dependencies array to be: [Profile, User, validateForm]", {"range": "1036", "text": "1037"}, {"range": "1038", "text": "963"}, {"range": "1039", "text": "1006"}, {"range": "1040", "text": "963"}, {"range": "1041", "text": "1006"}, {"range": "1042", "text": "963"}, {"range": "1043", "text": "1006"}, {"range": "1044", "text": "963"}, {"range": "1045", "text": "1006"}, {"range": "1046", "text": "963"}, {"range": "1047", "text": "1006"}, {"range": "1048", "text": "963"}, {"range": "1049", "text": "1006"}, {"range": "1050", "text": "963"}, {"range": "1051", "text": "1006"}, {"range": "1052", "text": "963"}, {"range": "1053", "text": "1006"}, {"range": "1054", "text": "963"}, {"range": "1055", "text": "1006"}, {"range": "1056", "text": "963"}, {"range": "1057", "text": "1006"}, {"range": "1058", "text": "963"}, {"range": "1059", "text": "1006"}, "Update the dependencies array to be: [formData, validateForm]", {"range": "1060", "text": "1061"}, "Update the dependencies array to be: [registrationSuccess, formData, validateForm]", {"range": "1062", "text": "1063"}, {"range": "1064", "text": "1061"}, {"range": "1065", "text": "963"}, {"range": "1066", "text": "1006"}, {"range": "1067", "text": "963"}, {"range": "1068", "text": "1006"}, "Update the dependencies array to be: [onSubmit]", {"range": "1069", "text": "1070"}, {"range": "1071", "text": "1032"}, [428, 437], "[fetchProfile, profile]", [1046, 1073], "[password, confirmPassword, validatePassword]", [1471, 1472], [1471, 1471], "\\", [934, 936], "[verify]", [8334, 8343], "[fetchUserData, profile]", [15559, 15560], [15559, 15559], [18739, 18748], [19607, 19609], [23902, 23903], [23902, 23902], [23904, 23905], [23904, 23904], [23912, 23913], [23912, 23912], [24011, 24012], [24011, 24011], [24013, 24014], [24013, 24013], [24021, 24022], [24021, 24021], [1620, 1634], "[fetchSearchResults, searchParams]", [1789, 1815], "[searchResults, activeTab, sortResultsByTab]", [5167, 5176], "[fetchProfileData, profile]", [6353, 6362], [16061, 16062], [16061, 16061], [4012, 4027], "[Profile, User, validateForm]", [5202, 5203], [5202, 5202], [7893, 7894], [7893, 7893], [7895, 7896], [7895, 7895], [7897, 7898], [7897, 7897], [19314, 19315], [19314, 19314], [19316, 19317], [19316, 19316], [7872, 7873], [7872, 7872], [7874, 7875], [7874, 7874], [7876, 7877], [7876, 7876], [19338, 19339], [19338, 19338], [19340, 19341], [19340, 19340], [1108, 1118], "[formData, validateForm]", [2086, 2117], "[registrationSuccess, formData, validateForm]", [653, 663], [2439, 2440], [2439, 2439], [2456, 2457], [2456, 2456], [141, 143], "[onSubmit]", [1197, 1206]]