{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\sections\\\\auth\\\\signup\\\\SignUpForm.js\",\n  _s = $RefreshSig$();\n// SignUpForm.js\nimport { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Register } from \"../../../AuthenticationData.ts\";\nimport { ToastContainer, toast } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport Register1 from \"./Register1\";\nimport Register2 from \"./Register2\";\nimport Register3 from \"./Register3\";\nimport RegisterConfirm from \"./RegisterConfirm\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function SignUpForm() {\n  _s();\n  const [currentPage, setCurrentPage] = useState(1);\n  const [formData, setFormData] = useState({\n    email: \"\",\n    firstName: \"\",\n    lastName: \"\",\n    userName: \"\",\n    password: \"\",\n    gender: \"\",\n    contactInfo: \"\",\n    categoryUser: \"\",\n    contactCategory: \"PhoneNumber\",\n    profilePicture: \"\"\n  });\n  const [registrationSuccess, setRegistrationSuccess] = useState(false);\n  const [isFormValid, setFormValid] = useState(false); // Validation state\n\n  const navigate = useNavigate();\n  const onSubmit = async () => {\n    try {\n      const response = await Register(formData);\n      if (response.error) {\n        throw new Error(response.error);\n      }\n      navigate(\"/VerifyMail\");\n      setRegistrationSuccess(true);\n    } catch (error) {\n      setCurrentPage(3);\n      setFormData(prevData => ({\n        ...prevData,\n        password: \"\"\n      }));\n      toast.error(error.message, {\n        position: \"top-center\",\n        autoClose: 1000\n      });\n    }\n  };\n  const handleNext = data => {\n    setFormData(prevData => ({\n      ...prevData,\n      ...data\n    }));\n    setCurrentPage(prevPage => prevPage + 1);\n  };\n  const handlePrevious = data => {\n    setFormData(prevData => ({\n      ...prevData,\n      ...data\n    }));\n    setCurrentPage(prevPage => prevPage - 1);\n  };\n  useEffect(() => {\n    validateForm();\n    if (registrationSuccess) {\n      toast.success(\"Registration successful!\", {\n        position: \"top-right\",\n        autoClose: 3000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true\n      });\n    }\n  }, [registrationSuccess, formData]);\n  const validateForm = () => {\n    const {\n      firstName,\n      lastName,\n      userName,\n      categoryUser\n    } = formData;\n    const isStep1Valid = firstName.trim() !== \"\" && lastName.trim() !== \"\" && userName.trim() !== \"\" && categoryUser.trim() !== \"\";\n    const isStep2Valid = formData.contactInfo.trim().length === 8;\n    setFormValid(isStep1Valid && isStep2Valid /* && ... */);\n  };\n  const renderPage = () => {\n    switch (currentPage) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Register1, {\n          onNext: handleNext,\n          data: formData,\n          isFormValid: isFormValid\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Register2, {\n          onNext: handleNext,\n          onPrevious: handlePrevious,\n          data: formData,\n          isFormValid: isFormValid\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(Register3, {\n          onNext: handleNext,\n          onPrevious: handlePrevious,\n          data: formData,\n          isFormValid: isFormValid\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: currentPage > 3 ? /*#__PURE__*/_jsxDEV(RegisterConfirm, {\n        onSubmit: onSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this) : renderPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(SignUpForm, \"jOnBc9eqQRpFKuHOCvov+CSkGy4=\", false, function () {\n  return [useNavigate];\n});\n_c = SignUpForm;\nvar _c;\n$RefreshReg$(_c, \"SignUpForm\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "Register", "ToastContainer", "toast", "Register1", "Register2", "Register3", "RegisterConfirm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SignUpForm", "_s", "currentPage", "setCurrentPage", "formData", "setFormData", "email", "firstName", "lastName", "userName", "password", "gender", "contactInfo", "categoryUser", "contactCategory", "profilePicture", "registrationSuccess", "setRegistrationSuccess", "isFormValid", "setFormValid", "navigate", "onSubmit", "response", "error", "Error", "prevData", "message", "position", "autoClose", "handleNext", "data", "prevPage", "handlePrevious", "validateForm", "success", "hideProgressBar", "closeOnClick", "pauseOnHover", "isStep1Valid", "trim", "isStep2Valid", "length", "renderPage", "onNext", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onPrevious", "children", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/sections/auth/signup/SignUpForm.js"], "sourcesContent": ["// SignUpForm.js\r\nimport { useState, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Register } from \"../../../AuthenticationData.ts\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport Register1 from \"./Register1\";\r\nimport Register2 from \"./Register2\";\r\nimport Register3 from \"./Register3\";\r\nimport RegisterConfirm from \"./RegisterConfirm\";\r\n\r\nexport default function SignUpForm() {\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    userName: \"\",\r\n    password: \"\",\r\n    gender: \"\",\r\n    contactInfo: \"\",\r\n    categoryUser: \"\",\r\n    contactCategory: \"PhoneNumber\",\r\n    profilePicture: \"\",\r\n  });\r\n  const [registrationSuccess, setRegistrationSuccess] = useState(false);\r\n  const [isFormValid, setFormValid] = useState(false); // Validation state\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const onSubmit = async () => {\r\n    try {\r\n      const response = await Register(formData);\r\n\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      navigate(\"/VerifyMail\");\r\n      setRegistrationSuccess(true);\r\n    } catch (error) {\r\n      setCurrentPage(3);\r\n      setFormData((prevData) => ({ ...prevData, password: \"\" }));\r\n      toast.error(error.message, {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    }\r\n  };\r\n  const handleNext = (data) => {\r\n    setFormData((prevData) => ({ ...prevData, ...data }));\r\n    setCurrentPage((prevPage) => prevPage + 1);\r\n  };\r\n\r\n  const handlePrevious = (data) => {\r\n    setFormData((prevData) => ({ ...prevData, ...data }));\r\n    setCurrentPage((prevPage) => prevPage - 1);\r\n  };\r\n\r\n  useEffect(() => {\r\n    validateForm();\r\n\r\n    if (registrationSuccess) {\r\n      toast.success(\"Registration successful!\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n      });\r\n    }\r\n  }, [registrationSuccess, formData]);\r\n\r\n  const validateForm = () => {\r\n    const { firstName, lastName, userName, categoryUser } = formData;\r\n\r\n    const isStep1Valid =\r\n      firstName.trim() !== \"\" &&\r\n      lastName.trim() !== \"\" &&\r\n      userName.trim() !== \"\" &&\r\n      categoryUser.trim() !== \"\";\r\n    const isStep2Valid = formData.contactInfo.trim().length === 8;\r\n\r\n    setFormValid(isStep1Valid && isStep2Valid /* && ... */);\r\n  };\r\n\r\n  const renderPage = () => {\r\n    switch (currentPage) {\r\n      case 1:\r\n        return (\r\n          <Register1\r\n            onNext={handleNext}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      case 2:\r\n        return (\r\n          <Register2\r\n            onNext={handleNext}\r\n            onPrevious={handlePrevious}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      case 3:\r\n        return (\r\n          <Register3\r\n            onNext={handleNext}\r\n            onPrevious={handlePrevious}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        {currentPage > 3 ? (\r\n          <RegisterConfirm onSubmit={onSubmit} />\r\n        ) : (\r\n          renderPage()\r\n        )}\r\n      </div>\r\n      <ToastContainer />\r\n    </>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhD,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE,aAAa;IAC9BC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,WAAW,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErD,MAAMkC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAE9B,MAAMiC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,QAAQ,CAACe,QAAQ,CAAC;MAEzC,IAAIkB,QAAQ,CAACC,KAAK,EAAE;QAClB,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAACC,KAAK,CAAC;MACjC;MAEAH,QAAQ,CAAC,aAAa,CAAC;MACvBH,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdpB,cAAc,CAAC,CAAC,CAAC;MACjBE,WAAW,CAAEoB,QAAQ,KAAM;QAAE,GAAGA,QAAQ;QAAEf,QAAQ,EAAE;MAAG,CAAC,CAAC,CAAC;MAC1DnB,KAAK,CAACgC,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;QACzBC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMC,UAAU,GAAIC,IAAI,IAAK;IAC3BzB,WAAW,CAAEoB,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,GAAGK;IAAK,CAAC,CAAC,CAAC;IACrD3B,cAAc,CAAE4B,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,cAAc,GAAIF,IAAI,IAAK;IAC/BzB,WAAW,CAAEoB,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,GAAGK;IAAK,CAAC,CAAC,CAAC;IACrD3B,cAAc,CAAE4B,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC5C,CAAC;EAED5C,SAAS,CAAC,MAAM;IACd8C,YAAY,CAAC,CAAC;IAEd,IAAIjB,mBAAmB,EAAE;MACvBzB,KAAK,CAAC2C,OAAO,CAAC,0BAA0B,EAAE;QACxCP,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfO,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,mBAAmB,EAAEZ,QAAQ,CAAC,CAAC;EAEnC,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MAAE1B,SAAS;MAAEC,QAAQ;MAAEC,QAAQ;MAAEI;IAAa,CAAC,GAAGT,QAAQ;IAEhE,MAAMkC,YAAY,GAChB/B,SAAS,CAACgC,IAAI,CAAC,CAAC,KAAK,EAAE,IACvB/B,QAAQ,CAAC+B,IAAI,CAAC,CAAC,KAAK,EAAE,IACtB9B,QAAQ,CAAC8B,IAAI,CAAC,CAAC,KAAK,EAAE,IACtB1B,YAAY,CAAC0B,IAAI,CAAC,CAAC,KAAK,EAAE;IAC5B,MAAMC,YAAY,GAAGpC,QAAQ,CAACQ,WAAW,CAAC2B,IAAI,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC;IAE7DtB,YAAY,CAACmB,YAAY,IAAIE,YAAY,CAAC,YAAY,CAAC;EACzD,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQxC,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEL,OAAA,CAACL,SAAS;UACRmD,MAAM,EAAEd,UAAW;UACnBC,IAAI,EAAE1B,QAAS;UACfc,WAAW,EAAEA;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAEN,KAAK,CAAC;QACJ,oBACElD,OAAA,CAACJ,SAAS;UACRkD,MAAM,EAAEd,UAAW;UACnBmB,UAAU,EAAEhB,cAAe;UAC3BF,IAAI,EAAE1B,QAAS;UACfc,WAAW,EAAEA;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAEN,KAAK,CAAC;QACJ,oBACElD,OAAA,CAACH,SAAS;UACRiD,MAAM,EAAEd,UAAW;UACnBmB,UAAU,EAAEhB,cAAe;UAC3BF,IAAI,EAAE1B,QAAS;UACfc,WAAW,EAAEA;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAEN;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACElD,OAAA,CAAAE,SAAA;IAAAkD,QAAA,gBACEpD,OAAA;MAAAoD,QAAA,EACG/C,WAAW,GAAG,CAAC,gBACdL,OAAA,CAACF,eAAe;QAAC0B,QAAQ,EAAEA;MAAS;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAEvCL,UAAU,CAAC;IACZ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNlD,OAAA,CAACP,cAAc;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAClB,CAAC;AAEP;AAAC9C,EAAA,CAxHuBD,UAAU;EAAA,QAiBfZ,WAAW;AAAA;AAAA8D,EAAA,GAjBNlD,UAAU;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}