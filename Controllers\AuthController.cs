using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using idigix.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using idigix.Services;
using idigix.Services.interfaces;

namespace idigix.Controllers;

[Route("api/[controller]")]
[ApiController]

public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly dbContext _cnx;
    private readonly ICouponService _couponService;
    private readonly IEmailSender _emailSender;
    private readonly IEmailTemplateService _emailTemplateService;
    private readonly IEncryptionService _encryptionService;
    private readonly ITokenService _tokenService;

    public AuthController(IConfiguration configuration, dbContext cnx, ICouponService couponService, IEmailSender emailSender, IEmailTemplateService emailTemplateService, IEncryptionService encryptionService, ITokenService tokenService)
    {
        _configuration = configuration;
        _cnx = cnx;
        _couponService = couponService;
        _emailSender = emailSender;
        _emailTemplateService = emailTemplateService;
        _encryptionService = encryptionService;
        _tokenService = tokenService;
    }

    [HttpPost("Login")]
    [AllowAnonymous]
    public async Task<IActionResult> Login([FromBody] logUser user)
    {
        User? userExist = await _cnx.Users
            .Include(u => u.Auth)
            .Include(w => w.Profile)
            .FirstOrDefaultAsync(u => (u.Email.ToLower() == user.Email.ToLower()) || (u.Profile.UserName == user.Email));

        if (userExist != null && verifyPassword(user.Password, userExist.Auth.Password))
        {
            // Check if email is verified
            if (!userExist.IsVerified)
            {
                return Unauthorized(new { error = "Please verify your email address before logging in. Check your inbox for the verification link." });
            }
            var issuer = _configuration["JwtSettings:Issuer"];
            var audience = _configuration["JwtSettings:Audience"];
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(JwtRegisteredClaimNames.NameId, userExist.Id.ToString()),
                    new Claim(JwtRegisteredClaimNames.Email, userExist.Email),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                }),
                Expires = DateTime.UtcNow.AddMinutes(120),
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature)
            };

            var accessTokenHandler = new JwtSecurityTokenHandler();
            var accessToken = accessTokenHandler.CreateToken(tokenDescriptor);
            var jwtAccessToken = accessTokenHandler.WriteToken(accessToken);
            var refreshToken = Guid.NewGuid().ToString();

            userExist.Auth.AccessToken = jwtAccessToken;
            userExist.Auth.RefreshToken = refreshToken;
            userExist.Auth.RefreshTokenExpiration = DateTime.UtcNow.AddHours(1).AddDays(1);
            await _cnx.SaveChangesAsync();

            return Ok(new { AccessToken = jwtAccessToken, RefreshToken = refreshToken, ExpiresIn = DateTime.UtcNow.AddMinutes(120) });
        }

        return Unauthorized(new { error = "Invalid email or password." });
    }

    [HttpPost("RefreshToken")]
    [AllowAnonymous]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest token)
    {
        if (token == null || string.IsNullOrEmpty(token.RefreshToken))
        {
            return BadRequest(new { error = "Refresh token is missing or empty" });
        }

        User? userExist = await _cnx.Users
            .Include(u => u.Auth)
            .FirstOrDefaultAsync(u => u.Auth.RefreshToken == token.RefreshToken);


        if (userExist != null)
        {
            var issuer = _configuration["JwtSettings:Issuer"];
            var audience = _configuration["JwtSettings:Audience"];
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(JwtRegisteredClaimNames.NameId, userExist.Id.ToString()),
                    new Claim(JwtRegisteredClaimNames.Email, userExist.Email),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                }),
                Expires = DateTime.UtcNow.AddMinutes(120),
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature)
            };

            var accessTokenHandler = new JwtSecurityTokenHandler();
            var accessToken = accessTokenHandler.CreateToken(tokenDescriptor);
            var jwtAccessToken = accessTokenHandler.WriteToken(accessToken);

            var NewRefreshToken = Guid.NewGuid().ToString();

            userExist.Auth.RefreshToken = NewRefreshToken;
            userExist.Auth.RefreshTokenExpiration = DateTime.UtcNow.AddHours(1).AddDays(1);

            await _cnx.SaveChangesAsync();

            return Ok(new { AccessToken = jwtAccessToken, RefreshToken = NewRefreshToken, ExpiresIn = DateTime.UtcNow.AddMinutes(120) });
        }


        return BadRequest(new { error = "invalid refresh token" });
    }

    [HttpPost("Register")]
    [AllowAnonymous]
    public async Task<IActionResult> Register([FromBody] registerUser user)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Check if the user already exists by email
        bool userExists = await _cnx.Users.AnyAsync(u => u.Email == user.Email);
        if (userExists)
        {
            return BadRequest("A user with this email already exists.");
        }

        bool usernameExists = await _cnx.Users
            .AnyAsync(u => u.Profile.UserName.ToLower() == user.UserName.ToLower());

        if (usernameExists)
        {
            return BadRequest("A user with this username already exists.");
        }

        // Additional validation for username format
        if (string.IsNullOrWhiteSpace(user.UserName) || user.UserName.Length < 3 || user.UserName.Length > 16)
        {
            return BadRequest("Username must be between 3 and 16 characters.");
        }

        // Check username contains only valid characters
        if (!System.Text.RegularExpressions.Regex.IsMatch(user.UserName, @"^[A-Za-z0-9_]+$"))
        {
            return BadRequest("Username can only contain letters, numbers, and underscores.");
        }

        // Generate JWT token
        var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[] { new Claim("email", user.Email) }),
            Expires = DateTime.UtcNow.AddHours(24),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var createdToken = tokenHandler.CreateToken(tokenDescriptor);
        var token = tokenHandler.WriteToken(createdToken);

        var newUser = new User
        {
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Category = "Free",
            IsVerified = false,
            Budget = 300,
        };

        var newAuth = new Auth
        {
            Password = HashPassword(user.Password),
            User = newUser,
            VerificationToken = token
        };

        var newContact = new Contact
        {
            ContactInfo = user.ContactCategory == "PhoneNumber" ? user.ContactInfo : _encryptionService.Encrypt(user.ContactInfo),
            Category = user.ContactCategory,
            Title = user.ContactCategory == "PhoneNumber" ? "Phone number" : null,
            User = newUser
        };

        var newProfile = new Profile
        {
            UserName = user.UserName,
            Gender = user.Gender,
            ProfilePicture = user.ProfilePicture,
            User = newUser,
            isSearch = true,
            Country = "Tunisia",
        };

        // Assign relationships
        newUser.Auth = newAuth;
        newUser.Profile = newProfile;
        newUser.Contacts = new List<Contact> { newContact };

        // Add user to database
        _cnx.Users.Add(newUser);
        await _cnx.SaveChangesAsync();

        // Send registration email
        var emailSubject = "Welcome to iDigics - Verify Your Account";
        var emailMessage = _emailTemplateService.GetAccountVerificationTemplate(token, newUser.Email);

        try
        {
            await _emailSender.SendEmailAsync(newUser.Email, emailSubject, emailMessage);
        }
        catch (Exception ex)
        {
            // Log the error but don't fail registration
            Console.WriteLine($"Failed to send verification email: {ex.Message}");
        }

        return Ok(new { message = "Registration successful. Please check your email to verify your account." });
    }

    [HttpPost("ForgotPassword")]
    [AllowAnonymous]
    public async Task<IActionResult> ForgotPassword([FromBody] string? email)
    {

        if (string.IsNullOrEmpty(email))
        {
            return BadRequest("Email cannot be empty");
        }
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        User? userExist = await _cnx.Users
            .Include(u => u.Auth)
            .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());
        if (userExist != null)
        {
            // Generate JWT token
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[] { new Claim("email", userExist.Email) }),
                Expires = DateTime.UtcNow.AddMinutes(10),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var createdToken = tokenHandler.CreateToken(tokenDescriptor);
            var token = tokenHandler.WriteToken(createdToken);

            // Send forgot password email
            var emailSubject = "Reset Your iDigics Password";
            var emailMessage = _emailTemplateService.GetPasswordResetTemplate(token, userExist.Email);

            await _emailSender.SendEmailAsync(userExist.Email, emailSubject, emailMessage);
            return Ok(emailMessage);
        }
        return BadRequest(new { error = "This email does not have an account." });
    }

    [HttpPost("ChangePassword")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] string? password)
    {

        if (string.IsNullOrEmpty(password))
        {
            return BadRequest("Password cannot be empty");
        }
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        User? userExist = await _cnx.Users
            .Include(u => u.Auth)
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (userExist != null && verifyPassword(password, userExist.Auth.Password))
        {
            // if (userExist != null && password == userExist.Auth.Password){
            // Generate JWT token
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[] { new Claim("email", userExist.Email) }),
                Expires = DateTime.UtcNow.AddHours(24),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var createdToken = tokenHandler.CreateToken(tokenDescriptor);
            var token = tokenHandler.WriteToken(createdToken);


            return Ok();
        }
        return Unauthorized(new { error = "Invalid password." });
    }

    [HttpPost("VerifyPasswordChanging/{token}")]
    [AllowAnonymous]
    public async Task<IActionResult> VerifyPasswordChanging([FromBody] string? confirmPassword, string token)
    {
        try
        {
            // Validate password input
            if (string.IsNullOrEmpty(confirmPassword))
            {
                return BadRequest(new { error = "Password cannot be empty." });
            }

            if (confirmPassword.Length < 8)
            {
                return BadRequest(new { error = "Password must be at least 8 characters long." });
            }

            // Validate password complexity
            if (!System.Text.RegularExpressions.Regex.IsMatch(confirmPassword, @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\]{};':""\\|,.<>\/?]).{8,}$"))
            {
                return BadRequest(new { error = "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character." });
            }

            if (!VerifyToken(token))
            {
                return BadRequest(new { error = "Invalid or malformed token." });
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);

            // Check token expiration
            var expirationClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "exp");
            if (expirationClaim == null || !long.TryParse(expirationClaim.Value, out long expirationUnixTime))
            {
                return BadRequest(new { error = "Invalid or missing expiration claim in token." });
            }

            var expirationDateTime = DateTimeOffset.FromUnixTimeSeconds(expirationUnixTime).UtcDateTime;
            if (DateTime.UtcNow > expirationDateTime)
            {
                return BadRequest(new { error = "Password reset token has expired. Please request a new password reset." });
            }

            var email = jwtToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value;

            if (string.IsNullOrEmpty(email))
            {
                return BadRequest(new { error = "Email not found in token." });
            }

            var user = await _cnx.Users.Include(u => u.Auth).FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

            if (user == null)
            {
                return BadRequest(new { error = "User account not found." });
            }

            // Hash and save the new password
            user.Auth.Password = HashPassword(confirmPassword);
            await _cnx.SaveChangesAsync();

            // Send password change confirmation email
            var emailSubject = "Password Successfully Changed - iDigics";
            var emailMessage = _emailTemplateService.GetPasswordChangedConfirmationTemplate(user.Email);
            await _emailSender.SendEmailAsync(user.Email, emailSubject, emailMessage);

            return Ok(new { message = "Password changed successfully." });
        }
        catch (Exception ex)
        {
            // Log the exception (you might want to use a proper logging framework)
            Console.WriteLine($"Error in VerifyPasswordChanging: {ex.Message}");
            return StatusCode(500, new { error = "An internal server error occurred. Please try again later." });
        }
    }


    [HttpPost("ResendVerificationEmail")]
    [AllowAnonymous]
    public async Task<IActionResult> ResendVerificationEmail([FromBody] string email)
    {
        if (string.IsNullOrEmpty(email))
        {
            return BadRequest(new { error = "Email cannot be empty." });
        }

        var user = await _cnx.Users.Include(u => u.Auth).FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

        if (user == null)
        {
            return BadRequest(new { error = "User not found." });
        }

        if (user.IsVerified)
        {
            return BadRequest(new { error = "Email is already verified." });
        }

        // Generate new verification token
        var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[] { new Claim("email", user.Email) }),
            Expires = DateTime.UtcNow.AddHours(24),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var createdToken = tokenHandler.CreateToken(tokenDescriptor);
        var token = tokenHandler.WriteToken(createdToken);

        // Update verification token in database
        user.Auth.VerificationToken = token;
        await _cnx.SaveChangesAsync();

        // Send verification email
        var emailSubject = "Welcome to iDigics - Verify Your Account";
        var emailMessage = _emailTemplateService.GetAccountVerificationTemplate(token, user.Email);

        try
        {
            await _emailSender.SendEmailAsync(user.Email, emailSubject, emailMessage);
            return Ok(new { message = "Verification email sent successfully." });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to send verification email: {ex.Message}");
            return StatusCode(500, new { error = "Failed to send verification email. Please try again later." });
        }
    }

    [HttpGet("VerifyEmail/{token}")]
    [AllowAnonymous]
    public async Task<IActionResult> VerifyEmail(string token)
    {
        if (!VerifyToken(token))
        {
            return BadRequest("Invalid token.");
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var email = jwtToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value;

        if (string.IsNullOrEmpty(email))
        {
            return BadRequest("Email not found in token.");
        }

        var user = await _cnx.Users.Include(u => u.Auth).FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

        if (user == null)
        {
            return BadRequest("User not found.");
        }

        if (!user.IsVerified)
        {
            user.IsVerified = true;
            await _cnx.SaveChangesAsync();
            return Ok("Email verified successfully.");
        }
        else
        {
            return BadRequest("Email already verified.");
        }
    }

    [HttpDelete("ChangePassword")]
    [Authorize]
    public async Task<IActionResult> DeleteUser([FromBody] int? Id)
    {
        if (Id == null)
            return BadRequest("no Id");

        var user = await _cnx.Users.FirstOrDefaultAsync(l => l.Id == Id);
        try
        {
            _cnx.Users.Remove(user);
            await _cnx.SaveChangesAsync();
        }
        catch (System.Exception)
        {
            throw;
        }

        return Ok("User deleted");
    }

    public bool verifyPassword(string enteredPassword, string hashedPassword)
    {
        if (enteredPassword == hashedPassword) return true;
        if (BCrypt.Net.BCrypt.Verify(enteredPassword, hashedPassword)) return true;
        return false;
    }

    public string HashPassword(string password)
    {
        // Generate a secure salt
        string salt = BCrypt.Net.BCrypt.GenerateSalt(12);

        string hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, salt);

        return hashedPassword;
    }

    private bool VerifyToken(string token)
    {
        var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);

        var tokenHandler = new JwtSecurityTokenHandler();
        try
        {
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            return true;
        }
        catch (SecurityTokenException ex)
        {
            // Log or handle the specific exception
            Console.WriteLine($"Token validation failed: {ex.Message}");
            return false;
        }
    }

    [HttpPost("VerifyAllExistingUsers")]
    [Authorize]
    public async Task<IActionResult> VerifyAllExistingUsers()
    {
        try
        {
            var unverifiedUsers = await _cnx.Users
                .Where(u => !u.IsVerified)
                .ToListAsync();

            if (!unverifiedUsers.Any())
            {
                return Ok(new { message = "All users are already verified.", count = 0 });
            }

            foreach (var user in unverifiedUsers)
            {
                user.IsVerified = true;
            }

            await _cnx.SaveChangesAsync();

            return Ok(new {
                message = $"Successfully verified {unverifiedUsers.Count} existing users.",
                count = unverifiedUsers.Count
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error verifying existing users: {ex.Message}");
            return StatusCode(500, new { error = "An error occurred while verifying existing users." });
        }
    }
}
