{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\pages\\\\TrackPage.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState, useCallback } from \"react\";\nimport { Avatar, Typography, Paper, Box, IconButton } from \"@mui/material\";\nimport { ArrowBack } from \"@mui/icons-material\";\nimport { GetProfilesFromLink } from \"../ProfileData.ts\";\nimport { useNavigate } from \"react-router-dom\";\nimport { PROFILE_URL } from \"../Context/config\";\nimport useResponsive from \"../hooks/useResponsive\";\nimport { styled } from \"@mui/material/styles\";\n\n// Styled components for the v3 design\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledContainer = styled(Box)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    display: \"flex\",\n    flexDirection: \"column\",\n    minHeight: \"100vh\",\n    background: \"linear-gradient(135deg, rgb(255, 113, 91), #f8e9e9)\",\n    padding: \"1rem\",\n    gap: \"1rem\",\n    fontFamily: \"'Nunito', sans-serif\",\n    [theme.breakpoints.up(\"md\")]: {\n      padding: \"1.5rem\",\n      gap: \"1.5rem\"\n    }\n  };\n});\n_c = StyledContainer;\nconst StyledHeader = styled(Paper)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    height: \"55px\",\n    backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n    backdropFilter: \"blur(5px)\",\n    borderRadius: \"12px\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"0 1rem\",\n    border: \"1px solid rgba(255, 255, 255, 0.3)\",\n    boxShadow: \"0 4px 15px rgba(100, 130, 140, 0.15)\",\n    [theme.breakpoints.up(\"md\")]: {\n      height: \"60px\",\n      padding: \"0 1.5rem\"\n    }\n  };\n});\n_c2 = StyledHeader;\nconst StyledProfileCard = styled(Paper)(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    backgroundColor: \"#FFFFFF\",\n    borderRadius: \"20px\",\n    boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\n    padding: \"1rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    textAlign: \"center\",\n    [theme.breakpoints.up(\"md\")]: {\n      padding: \"1.5rem\",\n      justifyContent: \"center\",\n      height: \"100%\"\n    }\n  };\n});\n_c3 = StyledProfileCard;\nconst StyledMetricsCard = styled(Paper)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    backgroundColor: \"#FFFFFF\",\n    borderRadius: \"20px\",\n    boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\n    padding: \"1rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    [theme.breakpoints.up(\"md\")]: {\n      padding: \"1.5rem\"\n    }\n  };\n});\n_c4 = StyledMetricsCard;\nconst StyledQRCard = styled(Paper)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    backgroundColor: \"#FFFFFF\",\n    borderRadius: \"20px\",\n    boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\n    padding: \"0.8rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    [theme.breakpoints.up(\"md\")]: {\n      padding: \"1rem\"\n    }\n  };\n});\n_c5 = StyledQRCard;\nexport const Track = () => {\n  _s();\n  var _Account$firstName, _Account$lastName, _Account$rate;\n  const navigate = useNavigate();\n  const isDesktop = useResponsive(\"up\", \"md\");\n  const [Account, setAccount] = useState({\n    email: \"\",\n    firstName: \"\",\n    id: 0,\n    lastName: \"\",\n    category: \"\",\n    profile: {\n      birthDate: \"\",\n      customLinks: null,\n      gender: \"\",\n      id: 0,\n      isPremium: false,\n      occupation: \"\",\n      premium: null,\n      profileCoverPicture: \"\",\n      profilePicture: \"\",\n      profilePictureFrame: 0,\n      socialLinks: null,\n      user: null,\n      userId: 0,\n      userName: \"\"\n    },\n    auth: {\n      id: 0,\n      password: \"\",\n      user: null,\n      userId: 8\n    },\n    rate: 0,\n    rateCount: 0,\n    rate_Skill_QualityOfWork: 0,\n    rate_Skill_CostEffectiveness: 0,\n    rate_Skill_Timeliness: 0,\n    rate_Skill_Communication: 0,\n    rate_Skill_Agility: 0\n  });\n  const Ratings = [{\n    name: \"Quality Of Work\",\n    score: Account.rate_Skill_QualityOfWork || 0,\n    icon: \"fas fa-star\"\n  }, {\n    name: \"Cost Effectiveness\",\n    score: Account.rate_Skill_CostEffectiveness || 0,\n    icon: \"fas fa-hand-holding-usd\"\n  }, {\n    name: \"Timeliness\",\n    score: Account.rate_Skill_Timeliness || 0,\n    icon: \"fas fa-hourglass-half\"\n  }, {\n    name: \"Communication\",\n    score: Account.rate_Skill_Communication || 0,\n    icon: \"fas fa-comments\"\n  }, {\n    name: \"Agility\",\n    score: Account.rate_Skill_Agility || 0,\n    icon: \"fas fa-rocket\"\n  }];\n  const fetchProfileData = useCallback(async () => {\n    const currentPath = window.location.pathname;\n    const searchQueryPart = currentPath.substring(currentPath.lastIndexOf(\"/\") + 1);\n    try {\n      const response = await GetProfilesFromLink(searchQueryPart);\n      setAccount(response.data);\n    } catch (error) {\n      navigate(\"/404\");\n    }\n  }, [navigate]);\n  useEffect(() => {\n    fetchProfileData();\n    // Execute fetch every minute\n    const intervalId = setInterval(fetchProfileData, 60000);\n    return () => clearInterval(intervalId);\n  }, [fetchProfileData]);\n\n  // Generate stars for rating display\n  const generateStars = rating => {\n    const stars = [];\n    const maxStars = 5;\n    for (let i = 1; i <= maxStars; i++) {\n      const scoreDifference = rating - (i - 1);\n      if (scoreDifference >= 0.75) {\n        stars.push( /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-star\",\n          style: {\n            color: \"#FFCA28\",\n            margin: \"0 2px\"\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 20\n        }, this));\n      } else if (scoreDifference >= 0.25) {\n        stars.push( /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-star-half-alt\",\n          style: {\n            color: \"#FFCA28\",\n            margin: \"0 2px\"\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 20\n        }, this));\n      } else {\n        stars.push( /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"far fa-star\",\n          style: {\n            color: \"#BDBDBD\",\n            margin: \"0 2px\"\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 20\n        }, this));\n      }\n    }\n    return stars;\n  };\n  return /*#__PURE__*/_jsxDEV(StyledContainer, {\n    children: [/*#__PURE__*/_jsxDEV(StyledHeader, {\n      elevation: 0,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => {\n            // Try to go back in history, fallback to dashboard if no history\n            if (window.history.length > 1) {\n              navigate(-1);\n            } else {\n              navigate(\"/admin/User\");\n            }\n          },\n          sx: {\n            color: \"#37474F\",\n            \"&:hover\": {\n              backgroundColor: \"rgba(55, 71, 79, 0.1)\"\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/assets/idigics_logo.png\",\n          alt: \"iDigics Logo\",\n          onClick: () => navigate(\"/\"),\n          style: {\n            height: isDesktop ? \"33px\" : \"30px\",\n            width: \"auto\",\n            cursor: \"pointer\",\n            transition: \"opacity 0.2s ease\"\n          },\n          onMouseEnter: e => e.target.style.opacity = \"0.8\",\n          onMouseLeave: e => e.target.style.opacity = \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"grid\",\n        gridTemplateColumns: isDesktop ? \"1fr 1.6fr\" : \"1fr\",\n        gap: isDesktop ? \"1.5rem\" : \"1rem\",\n        flexGrow: 1,\n        minHeight: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(StyledProfileCard, {\n        elevation: 0,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            width: isDesktop ? \"clamp(110px, 15vh, 130px)\" : \"90px\",\n            height: isDesktop ? \"clamp(110px, 15vh, 130px)\" : \"90px\",\n            marginBottom: isDesktop ? \"clamp(1rem, 2vh, 1.5rem)\" : \"0.8rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: Account.profile.profilePicture,\n            sx: {\n              width: \"100%\",\n              height: \"100%\",\n              border: isDesktop ? \"5px solid #FFFFFF\" : \"4px solid #FFFFFF\",\n              boxShadow: \"0 4px 10px rgba(0,0,0,0.1)\",\n              fontSize: isDesktop ? \"clamp(2.8rem, 5vh, 3.5rem)\" : \"2.2rem\",\n              fontWeight: 700,\n              backgroundColor: \"#78909C\",\n              color: \"white\"\n            },\n            children: !Account.profile.profilePicture && `${((_Account$firstName = Account.firstName) === null || _Account$firstName === void 0 ? void 0 : _Account$firstName[0]) || ''}${((_Account$lastName = Account.lastName) === null || _Account$lastName === void 0 ? void 0 : _Account$lastName[0]) || ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: \"absolute\",\n              inset: isDesktop ? \"-6px\" : \"-4px\",\n              border: isDesktop ? \"3px solid rgb(255, 113, 91)\" : \"2px solid rgb(255, 113, 91)\",\n              borderRadius: \"50%\",\n              opacity: 0.6\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: isDesktop ? \"clamp(1.6rem, 3vh, 1.8rem)\" : \"1.4rem\",\n            fontWeight: 800,\n            marginBottom: \"0.1rem\",\n            color: \"#37474F\"\n          },\n          children: [Account.firstName, \" \", Account.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: isDesktop ? \"clamp(0.9rem, 1.8vh, 1rem)\" : \"0.85rem\",\n            fontWeight: 600,\n            color: \"rgb(255, 113, 91)\",\n            marginBottom: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.8rem)\" : \"1rem\"\n          },\n          children: Account.profile.occupation || \"Freelancer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            marginTop: isDesktop ? \"clamp(0.8rem, 1.5vh, 1rem)\" : \"0.5rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              fontSize: isDesktop ? \"clamp(3.8rem, 7vh, 4.5rem)\" : \"3.2rem\",\n              fontWeight: 800,\n              color: \"rgb(255, 113, 91)\",\n              lineHeight: 1,\n              marginBottom: \"0.1rem\"\n            },\n            children: [((_Account$rate = Account.rate) === null || _Account$rate === void 0 ? void 0 : _Account$rate.toFixed(1)) || \"0.0\", /*#__PURE__*/_jsxDEV(Typography, {\n              component: \"span\",\n              sx: {\n                fontSize: isDesktop ? \"clamp(1.3rem, 2.5vh, 1.5rem)\" : \"1.1rem\",\n                fontWeight: 400,\n                color: \"#78909C\"\n              },\n              children: \"/5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              fontSize: isDesktop ? \"clamp(1.4rem, 2.8vh, 1.6rem)\" : \"1.2rem\",\n              margin: isDesktop ? \"clamp(0.4rem, 1vh, 0.6rem) 0\" : \"0.3rem 0\"\n            },\n            children: generateStars(Account.rate || 0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              fontSize: isDesktop ? \"clamp(0.85rem, 1.7vh, 0.95rem)\" : \"0.8rem\",\n              color: \"#78909C\",\n              fontWeight: 600\n            },\n            children: [\"Based on \", Account.rateCount || 0, \" Reviews\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: isDesktop ? \"1.5rem\" : \"1rem\",\n          minHeight: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(StyledMetricsCard, {\n          elevation: 0,\n          sx: {\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              fontSize: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.4rem)\" : \"1.1rem\",\n              fontWeight: 700,\n              marginBottom: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.5rem)\" : \"1rem\",\n              color: \"#37474F\"\n            },\n            children: \"Performance Breakdown\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              gap: isDesktop ? \"clamp(0.8rem, 1.5vh, 1rem)\" : \"0.6rem\",\n              flexGrow: 1,\n              justifyContent: \"space-evenly\"\n            },\n            children: Ratings.map((metric, index) => {\n              var _metric$score;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: isDesktop ? \"0.8rem\" : \"0.6rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: isDesktop ? \"22px\" : \"20px\",\n                    fontSize: isDesktop ? \"clamp(1rem, 1.8vh, 1.1rem)\" : \"1rem\",\n                    color: \"rgb(255, 113, 91)\",\n                    textAlign: \"center\",\n                    flexShrink: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: metric.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: isDesktop ? \"0.8rem\" : \"0.6rem\",\n                    flex: 1,\n                    minWidth: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      flexBasis: isDesktop ? \"clamp(110px, 15vw, 130px)\" : \"100px\",\n                      fontWeight: 600,\n                      fontSize: isDesktop ? \"clamp(0.9rem, 1.7vh, 0.95rem)\" : \"0.85rem\",\n                      color: \"#37474F\",\n                      whiteSpace: \"nowrap\",\n                      overflow: \"hidden\",\n                      textOverflow: \"ellipsis\"\n                    },\n                    children: metric.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flexBasis: isDesktop ? \"clamp(100px, 20vw, 180px)\" : \"80px\",\n                      height: isDesktop ? \"11px\" : \"9px\",\n                      backgroundColor: \"#ECEFF1\",\n                      borderRadius: isDesktop ? \"6px\" : \"5px\",\n                      overflow: \"hidden\",\n                      minWidth: isDesktop ? \"60px\" : \"40px\",\n                      position: \"relative\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        height: \"100%\",\n                        background: \"linear-gradient(90deg, #FFCA28, #FFA000)\",\n                        borderRadius: isDesktop ? \"6px\" : \"5px\",\n                        width: `${Math.min(metric.score / 5 * 100, 100)}%`,\n                        transition: \"width 1s cubic-bezier(0.34, 1.56, 0.64, 1)\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      flexBasis: isDesktop ? \"35px\" : \"30px\",\n                      fontWeight: 700,\n                      fontSize: isDesktop ? \"clamp(0.9rem, 1.8vh, 1rem)\" : \"0.85rem\",\n                      color: \"#37474F\",\n                      textAlign: \"right\",\n                      flexShrink: 0\n                    },\n                    children: ((_metric$score = metric.score) === null || _metric$score === void 0 ? void 0 : _metric$score.toFixed(1)) || \"0.0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StyledQRCard, {\n          elevation: 0,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: isDesktop ? \"clamp(150px, 20vh, 180px)\" : \"130px\",\n              height: isDesktop ? \"clamp(150px, 20vh, 180px)\" : \"130px\",\n              backgroundColor: \"#fff\",\n              padding: isDesktop ? \"8px\" : \"6px\",\n              borderRadius: \"12px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n              ref: canvas => {\n                if (canvas && Account.profile.userName) {\n                  // Clear any existing content\n                  const ctx = canvas.getContext(\"2d\");\n                  ctx.clearRect(0, 0, canvas.width, canvas.height);\n                  import(\"qrcode\").then(QRCode => {\n                    const qrSize = isDesktop ? 164 : 118;\n                    canvas.width = qrSize;\n                    canvas.height = qrSize;\n                    QRCode.toCanvas(canvas, `${PROFILE_URL}/Profile/${Account.profile.userName}`, {\n                      width: qrSize,\n                      margin: 1,\n                      color: {\n                        dark: \"#2c3e50\",\n                        light: \"#ffffff\"\n                      },\n                      errorCorrectionLevel: 'M',\n                      type: 'image/png',\n                      quality: 0.92,\n                      rendererOpts: {\n                        quality: 0.92\n                      }\n                    }, error => {\n                      if (error) {\n                        console.error(\"QR Code generation error:\", error);\n                        return;\n                      }\n\n                      // Add logo overlay\n                      const ctx = canvas.getContext(\"2d\");\n                      const logo = new Image();\n                      logo.crossOrigin = \"anonymous\";\n                      logo.onload = () => {\n                        // Calculate logo size and position (center of QR code)\n                        const logoSize = isDesktop ? 32 : 24;\n                        const x = (canvas.width - logoSize) / 2;\n                        const y = (canvas.height - logoSize) / 2;\n\n                        // Draw white background circle for logo\n                        ctx.fillStyle = \"#ffffff\";\n                        ctx.beginPath();\n                        ctx.arc(x + logoSize / 2, y + logoSize / 2, logoSize / 2 + 2, 0, 2 * Math.PI);\n                        ctx.fill();\n\n                        // Draw the logo\n                        ctx.drawImage(logo, x, y, logoSize, logoSize);\n                      };\n                      logo.onerror = () => {\n                        console.log(\"Logo not found, QR code generated without logo\");\n                      };\n                      logo.src = \"/assets/idigics_logo.png\";\n                    });\n                  }).catch(error => {\n                    console.error(\"Failed to load QR code library:\", error);\n                  });\n                }\n              },\n              style: {\n                borderRadius: \"6px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(Track, \"8AIYvx6IraE3gZjzJ30r2fJV5IU=\", false, function () {\n  return [useNavigate, useResponsive];\n});\n_c6 = Track;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledContainer\");\n$RefreshReg$(_c2, \"StyledHeader\");\n$RefreshReg$(_c3, \"StyledProfileCard\");\n$RefreshReg$(_c4, \"StyledMetricsCard\");\n$RefreshReg$(_c5, \"StyledQRCard\");\n$RefreshReg$(_c6, \"Track\");", "map": {"version": 3, "names": ["useEffect", "useState", "useCallback", "Avatar", "Typography", "Paper", "Box", "IconButton", "ArrowBack", "GetProfilesFromLink", "useNavigate", "PROFILE_URL", "useResponsive", "styled", "jsxDEV", "_jsxDEV", "StyledContainer", "_ref", "theme", "display", "flexDirection", "minHeight", "background", "padding", "gap", "fontFamily", "breakpoints", "up", "_c", "StyledHeader", "_ref2", "height", "backgroundColor", "<PERSON><PERSON>ilter", "borderRadius", "justifyContent", "alignItems", "border", "boxShadow", "_c2", "StyledProfileCard", "_ref3", "textAlign", "_c3", "StyledMetricsCard", "_ref4", "_c4", "StyledQRCard", "_ref5", "_c5", "Track", "_s", "_Account$firstName", "_Account$lastName", "_Account$rate", "navigate", "isDesktop", "Account", "setAccount", "email", "firstName", "id", "lastName", "category", "profile", "birthDate", "customLinks", "gender", "isPremium", "occupation", "premium", "profileCoverPicture", "profilePicture", "profilePictureFrame", "socialLinks", "user", "userId", "userName", "auth", "password", "rate", "rateCount", "rate_Skill_QualityOfWork", "rate_Skill_CostEffectiveness", "rate_Skill_Timeliness", "rate_Skill_Communication", "rate_Skill_Agility", "Ratings", "name", "score", "icon", "fetchProfileData", "currentPath", "window", "location", "pathname", "searchQueryPart", "substring", "lastIndexOf", "response", "data", "error", "intervalId", "setInterval", "clearInterval", "generateStars", "rating", "stars", "maxStars", "i", "scoreDifference", "push", "className", "style", "color", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "elevation", "sx", "onClick", "history", "length", "src", "alt", "width", "cursor", "transition", "onMouseEnter", "e", "target", "opacity", "onMouseLeave", "gridTemplateColumns", "flexGrow", "position", "marginBottom", "fontSize", "fontWeight", "inset", "marginTop", "lineHeight", "toFixed", "component", "map", "metric", "index", "_metric$score", "flexShrink", "flex", "min<PERSON><PERSON><PERSON>", "flexBasis", "whiteSpace", "overflow", "textOverflow", "Math", "min", "ref", "canvas", "ctx", "getContext", "clearRect", "then", "QRCode", "qrSize", "to<PERSON><PERSON><PERSON>", "dark", "light", "errorCorrectionLevel", "type", "quality", "rendererOpts", "console", "logo", "Image", "crossOrigin", "onload", "logoSize", "x", "y", "fillStyle", "beginPath", "arc", "PI", "fill", "drawImage", "onerror", "log", "catch", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/pages/TrackPage.js"], "sourcesContent": ["import { useEffect, useState, useCallback } from \"react\";\r\nimport { Avatar, Typography, Paper, Box, IconButton } from \"@mui/material\";\r\nimport { ArrowBack } from \"@mui/icons-material\";\r\nimport { GetProfilesFromLink } from \"../ProfileData.ts\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { PROFILE_URL } from \"../Context/config\";\r\nimport useResponsive from \"../hooks/useResponsive\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\n// Styled components for the v3 design\r\nconst StyledContainer = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  minHeight: \"100vh\",\r\n  background: \"linear-gradient(135deg, rgb(255, 113, 91), #f8e9e9)\",\r\n  padding: \"1rem\",\r\n  gap: \"1rem\",\r\n  fontFamily: \"'Nunito', sans-serif\",\r\n  [theme.breakpoints.up(\"md\")]: {\r\n    padding: \"1.5rem\",\r\n    gap: \"1.5rem\",\r\n  },\r\n}));\r\n\r\nconst StyledHeader = styled(Paper)(({ theme }) => ({\r\n  height: \"55px\",\r\n  backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n  backdropFilter: \"blur(5px)\",\r\n  borderRadius: \"12px\",\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n  alignItems: \"center\",\r\n  padding: \"0 1rem\",\r\n  border: \"1px solid rgba(255, 255, 255, 0.3)\",\r\n  boxShadow: \"0 4px 15px rgba(100, 130, 140, 0.15)\",\r\n  [theme.breakpoints.up(\"md\")]: {\r\n    height: \"60px\",\r\n    padding: \"0 1.5rem\",\r\n  },\r\n}));\r\n\r\nconst StyledProfileCard = styled(Paper)(({ theme }) => ({\r\n  backgroundColor: \"#FFFFFF\",\r\n  borderRadius: \"20px\",\r\n  boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\r\n  padding: \"1rem\",\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  textAlign: \"center\",\r\n  [theme.breakpoints.up(\"md\")]: {\r\n    padding: \"1.5rem\",\r\n    justifyContent: \"center\",\r\n    height: \"100%\",\r\n  },\r\n}));\r\n\r\nconst StyledMetricsCard = styled(Paper)(({ theme }) => ({\r\n  backgroundColor: \"#FFFFFF\",\r\n  borderRadius: \"20px\",\r\n  boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\r\n  padding: \"1rem\",\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  [theme.breakpoints.up(\"md\")]: {\r\n    padding: \"1.5rem\",\r\n  },\r\n}));\r\n\r\nconst StyledQRCard = styled(Paper)(({ theme }) => ({\r\n  backgroundColor: \"#FFFFFF\",\r\n  borderRadius: \"20px\",\r\n  boxShadow: \"0 6px 20px rgba(100, 130, 140, 0.15)\",\r\n  padding: \"0.8rem\",\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  [theme.breakpoints.up(\"md\")]: {\r\n    padding: \"1rem\",\r\n  },\r\n}));\r\n\r\nexport const Track = () => {\r\n  const navigate = useNavigate();\r\n  const isDesktop = useResponsive(\"up\", \"md\");\r\n  const [Account, setAccount] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    id: 0,\r\n    lastName: \"\",\r\n    category: \"\",\r\n    profile: {\r\n      birthDate: \"\",\r\n      customLinks: null,\r\n      gender: \"\",\r\n      id: 0,\r\n      isPremium: false,\r\n      occupation: \"\",\r\n      premium: null,\r\n      profileCoverPicture: \"\",\r\n      profilePicture: \"\",\r\n      profilePictureFrame: 0,\r\n      socialLinks: null,\r\n      user: null,\r\n      userId: 0,\r\n      userName: \"\",\r\n    },\r\n    auth: {\r\n      id: 0,\r\n      password: \"\",\r\n      user: null,\r\n      userId: 8,\r\n    },\r\n    rate: 0,\r\n    rateCount: 0,\r\n    rate_Skill_QualityOfWork: 0,\r\n    rate_Skill_CostEffectiveness: 0,\r\n    rate_Skill_Timeliness: 0,\r\n    rate_Skill_Communication: 0,\r\n    rate_Skill_Agility: 0,\r\n  });\r\n\r\n  const Ratings = [\r\n    {\r\n      name: \"Quality Of Work\",\r\n      score: Account.rate_Skill_QualityOfWork || 0,\r\n      icon: \"fas fa-star\",\r\n    },\r\n    {\r\n      name: \"Cost Effectiveness\",\r\n      score: Account.rate_Skill_CostEffectiveness || 0,\r\n      icon: \"fas fa-hand-holding-usd\",\r\n    },\r\n    {\r\n      name: \"Timeliness\",\r\n      score: Account.rate_Skill_Timeliness || 0,\r\n      icon: \"fas fa-hourglass-half\",\r\n    },\r\n    {\r\n      name: \"Communication\",\r\n      score: Account.rate_Skill_Communication || 0,\r\n      icon: \"fas fa-comments\",\r\n    },\r\n    {\r\n      name: \"Agility\",\r\n      score: Account.rate_Skill_Agility || 0,\r\n      icon: \"fas fa-rocket\",\r\n    },\r\n  ];\r\n\r\n  const fetchProfileData = useCallback(async () => {\r\n    const currentPath = window.location.pathname;\r\n    const searchQueryPart = currentPath.substring(\r\n      currentPath.lastIndexOf(\"/\") + 1\r\n    );\r\n    try {\r\n      const response = await GetProfilesFromLink(searchQueryPart);\r\n      setAccount(response.data);\r\n    } catch (error) {\r\n      navigate(\"/404\");\r\n    }\r\n  }, [navigate]);\r\n\r\n  useEffect(() => {\r\n    fetchProfileData();\r\n    // Execute fetch every minute\r\n    const intervalId = setInterval(fetchProfileData, 60000);\r\n    return () => clearInterval(intervalId);\r\n  }, [fetchProfileData]);\r\n\r\n  // Generate stars for rating display\r\n  const generateStars = (rating) => {\r\n    const stars = [];\r\n    const maxStars = 5;\r\n    for (let i = 1; i <= maxStars; i++) {\r\n      const scoreDifference = rating - (i - 1);\r\n      if (scoreDifference >= 0.75) {\r\n        stars.push(<i key={i} className=\"fas fa-star\" style={{ color: \"#FFCA28\", margin: \"0 2px\" }} />);\r\n      } else if (scoreDifference >= 0.25) {\r\n        stars.push(<i key={i} className=\"fas fa-star-half-alt\" style={{ color: \"#FFCA28\", margin: \"0 2px\" }} />);\r\n      } else {\r\n        stars.push(<i key={i} className=\"far fa-star\" style={{ color: \"#BDBDBD\", margin: \"0 2px\" }} />);\r\n      }\r\n    }\r\n    return stars;\r\n  };\r\n\r\n  return (\r\n    <StyledContainer>\r\n      {/* Header */}\r\n      <StyledHeader elevation={0}>\r\n        <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n          <IconButton\r\n            onClick={() => {\r\n              // Try to go back in history, fallback to dashboard if no history\r\n              if (window.history.length > 1) {\r\n                navigate(-1);\r\n              } else {\r\n                navigate(\"/admin/User\");\r\n              }\r\n            }}\r\n            sx={{\r\n              color: \"#37474F\",\r\n              \"&:hover\": {\r\n                backgroundColor: \"rgba(55, 71, 79, 0.1)\",\r\n              },\r\n            }}\r\n          >\r\n            <ArrowBack />\r\n          </IconButton>\r\n          <img\r\n            src=\"/assets/idigics_logo.png\"\r\n            alt=\"iDigics Logo\"\r\n            onClick={() => navigate(\"/\")}\r\n            style={{\r\n              height: isDesktop ? \"33px\" : \"30px\",\r\n              width: \"auto\",\r\n              cursor: \"pointer\",\r\n              transition: \"opacity 0.2s ease\",\r\n            }}\r\n            onMouseEnter={(e) => e.target.style.opacity = \"0.8\"}\r\n            onMouseLeave={(e) => e.target.style.opacity = \"1\"}\r\n          />\r\n        </Box>\r\n        <Box sx={{ display: \"flex\", alignItems: \"center\" }}>\r\n          {/* Clock or additional header content can go here */}\r\n        </Box>\r\n      </StyledHeader>\r\n\r\n      {/* Main Content Grid */}\r\n      <Box sx={{\r\n        display: \"grid\",\r\n        gridTemplateColumns: isDesktop ? \"1fr 1.6fr\" : \"1fr\",\r\n        gap: isDesktop ? \"1.5rem\" : \"1rem\",\r\n        flexGrow: 1,\r\n        minHeight: 0\r\n      }}>\r\n        {/* Profile Card */}\r\n        <StyledProfileCard elevation={0}>\r\n          <Box sx={{\r\n            position: \"relative\",\r\n            width: isDesktop ? \"clamp(110px, 15vh, 130px)\" : \"90px\",\r\n            height: isDesktop ? \"clamp(110px, 15vh, 130px)\" : \"90px\",\r\n            marginBottom: isDesktop ? \"clamp(1rem, 2vh, 1.5rem)\" : \"0.8rem\"\r\n          }}>\r\n            <Avatar\r\n              src={Account.profile.profilePicture}\r\n              sx={{\r\n                width: \"100%\",\r\n                height: \"100%\",\r\n                border: isDesktop ? \"5px solid #FFFFFF\" : \"4px solid #FFFFFF\",\r\n                boxShadow: \"0 4px 10px rgba(0,0,0,0.1)\",\r\n                fontSize: isDesktop ? \"clamp(2.8rem, 5vh, 3.5rem)\" : \"2.2rem\",\r\n                fontWeight: 700,\r\n                backgroundColor: \"#78909C\",\r\n                color: \"white\"\r\n              }}\r\n            >\r\n              {!Account.profile.profilePicture &&\r\n                `${Account.firstName?.[0] || ''}${Account.lastName?.[0] || ''}`\r\n              }\r\n            </Avatar>\r\n            <Box sx={{\r\n              position: \"absolute\",\r\n              inset: isDesktop ? \"-6px\" : \"-4px\",\r\n              border: isDesktop ? \"3px solid rgb(255, 113, 91)\" : \"2px solid rgb(255, 113, 91)\",\r\n              borderRadius: \"50%\",\r\n              opacity: 0.6\r\n            }} />\r\n          </Box>\r\n\r\n          <Typography sx={{\r\n            fontSize: isDesktop ? \"clamp(1.6rem, 3vh, 1.8rem)\" : \"1.4rem\",\r\n            fontWeight: 800,\r\n            marginBottom: \"0.1rem\",\r\n            color: \"#37474F\"\r\n          }}>\r\n            {Account.firstName} {Account.lastName}\r\n          </Typography>\r\n\r\n          <Typography sx={{\r\n            fontSize: isDesktop ? \"clamp(0.9rem, 1.8vh, 1rem)\" : \"0.85rem\",\r\n            fontWeight: 600,\r\n            color: \"rgb(255, 113, 91)\",\r\n            marginBottom: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.8rem)\" : \"1rem\"\r\n          }}>\r\n            {Account.profile.occupation || \"Freelancer\"}\r\n          </Typography>\r\n\r\n          <Box sx={{ marginTop: isDesktop ? \"clamp(0.8rem, 1.5vh, 1rem)\" : \"0.5rem\" }}>\r\n            <Typography sx={{\r\n              fontSize: isDesktop ? \"clamp(3.8rem, 7vh, 4.5rem)\" : \"3.2rem\",\r\n              fontWeight: 800,\r\n              color: \"rgb(255, 113, 91)\",\r\n              lineHeight: 1,\r\n              marginBottom: \"0.1rem\"\r\n            }}>\r\n              {Account.rate?.toFixed(1) || \"0.0\"}\r\n              <Typography component=\"span\" sx={{\r\n                fontSize: isDesktop ? \"clamp(1.3rem, 2.5vh, 1.5rem)\" : \"1.1rem\",\r\n                fontWeight: 400,\r\n                color: \"#78909C\"\r\n              }}>\r\n                /5\r\n              </Typography>\r\n            </Typography>\r\n\r\n            <Box sx={{\r\n              fontSize: isDesktop ? \"clamp(1.4rem, 2.8vh, 1.6rem)\" : \"1.2rem\",\r\n              margin: isDesktop ? \"clamp(0.4rem, 1vh, 0.6rem) 0\" : \"0.3rem 0\"\r\n            }}>\r\n              {generateStars(Account.rate || 0)}\r\n            </Box>\r\n\r\n            <Typography sx={{\r\n              fontSize: isDesktop ? \"clamp(0.85rem, 1.7vh, 0.95rem)\" : \"0.8rem\",\r\n              color: \"#78909C\",\r\n              fontWeight: 600\r\n            }}>\r\n              Based on {Account.rateCount || 0} Reviews\r\n            </Typography>\r\n          </Box>\r\n        </StyledProfileCard>\r\n\r\n        {/* Right Column - Metrics and QR */}\r\n        <Box sx={{\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          gap: isDesktop ? \"1.5rem\" : \"1rem\",\r\n          minHeight: 0\r\n        }}>\r\n          {/* Performance Metrics */}\r\n          <StyledMetricsCard elevation={0} sx={{ flexGrow: 1 }}>\r\n            <Typography sx={{\r\n              fontSize: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.4rem)\" : \"1.1rem\",\r\n              fontWeight: 700,\r\n              marginBottom: isDesktop ? \"clamp(1.2rem, 2.5vh, 1.5rem)\" : \"1rem\",\r\n              color: \"#37474F\"\r\n            }}>\r\n              Performance Breakdown\r\n            </Typography>\r\n\r\n            <Box sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              gap: isDesktop ? \"clamp(0.8rem, 1.5vh, 1rem)\" : \"0.6rem\",\r\n              flexGrow: 1,\r\n              justifyContent: \"space-evenly\"\r\n            }}>\r\n              {Ratings.map((metric, index) => (\r\n                <Box key={index} sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: isDesktop ? \"0.8rem\" : \"0.6rem\"\r\n                }}>\r\n                  <Box sx={{\r\n                    width: isDesktop ? \"22px\" : \"20px\",\r\n                    fontSize: isDesktop ? \"clamp(1rem, 1.8vh, 1.1rem)\" : \"1rem\",\r\n                    color: \"rgb(255, 113, 91)\",\r\n                    textAlign: \"center\",\r\n                    flexShrink: 0\r\n                  }}>\r\n                    <i className={metric.icon} />\r\n                  </Box>\r\n\r\n                  <Box sx={{\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    gap: isDesktop ? \"0.8rem\" : \"0.6rem\",\r\n                    flex: 1,\r\n                    minWidth: 0\r\n                  }}>\r\n                    <Typography sx={{\r\n                      flexBasis: isDesktop ? \"clamp(110px, 15vw, 130px)\" : \"100px\",\r\n                      fontWeight: 600,\r\n                      fontSize: isDesktop ? \"clamp(0.9rem, 1.7vh, 0.95rem)\" : \"0.85rem\",\r\n                      color: \"#37474F\",\r\n                      whiteSpace: \"nowrap\",\r\n                      overflow: \"hidden\",\r\n                      textOverflow: \"ellipsis\"\r\n                    }}>\r\n                      {metric.name}\r\n                    </Typography>\r\n\r\n                    <Box sx={{\r\n                      flexBasis: isDesktop ? \"clamp(100px, 20vw, 180px)\" : \"80px\",\r\n                      height: isDesktop ? \"11px\" : \"9px\",\r\n                      backgroundColor: \"#ECEFF1\",\r\n                      borderRadius: isDesktop ? \"6px\" : \"5px\",\r\n                      overflow: \"hidden\",\r\n                      minWidth: isDesktop ? \"60px\" : \"40px\",\r\n                      position: \"relative\"\r\n                    }}>\r\n                      <Box sx={{\r\n                        height: \"100%\",\r\n                        background: \"linear-gradient(90deg, #FFCA28, #FFA000)\",\r\n                        borderRadius: isDesktop ? \"6px\" : \"5px\",\r\n                        width: `${Math.min((metric.score / 5) * 100, 100)}%`,\r\n                        transition: \"width 1s cubic-bezier(0.34, 1.56, 0.64, 1)\"\r\n                      }} />\r\n                    </Box>\r\n\r\n                    <Typography sx={{\r\n                      flexBasis: isDesktop ? \"35px\" : \"30px\",\r\n                      fontWeight: 700,\r\n                      fontSize: isDesktop ? \"clamp(0.9rem, 1.8vh, 1rem)\" : \"0.85rem\",\r\n                      color: \"#37474F\",\r\n                      textAlign: \"right\",\r\n                      flexShrink: 0\r\n                    }}>\r\n                      {metric.score?.toFixed(1) || \"0.0\"}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              ))}\r\n            </Box>\r\n          </StyledMetricsCard>\r\n\r\n          {/* QR Code */}\r\n          <StyledQRCard elevation={0}>\r\n            <Box sx={{\r\n              width: isDesktop ? \"clamp(150px, 20vh, 180px)\" : \"130px\",\r\n              height: isDesktop ? \"clamp(150px, 20vh, 180px)\" : \"130px\",\r\n              backgroundColor: \"#fff\",\r\n              padding: isDesktop ? \"8px\" : \"6px\",\r\n              borderRadius: \"12px\",\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"center\"\r\n            }}>\r\n              <canvas\r\n                ref={(canvas) => {\r\n                  if (canvas && Account.profile.userName) {\r\n                    // Clear any existing content\r\n                    const ctx = canvas.getContext(\"2d\");\r\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n\r\n                    import(\"qrcode\").then((QRCode) => {\r\n                      const qrSize = isDesktop ? 164 : 118;\r\n                      canvas.width = qrSize;\r\n                      canvas.height = qrSize;\r\n\r\n                      QRCode.toCanvas(\r\n                        canvas,\r\n                        `${PROFILE_URL}/Profile/${Account.profile.userName}`,\r\n                        {\r\n                          width: qrSize,\r\n                          margin: 1,\r\n                          color: {\r\n                            dark: \"#2c3e50\",\r\n                            light: \"#ffffff\",\r\n                          },\r\n                          errorCorrectionLevel: 'M',\r\n                          type: 'image/png',\r\n                          quality: 0.92,\r\n                          rendererOpts: {\r\n                            quality: 0.92\r\n                          }\r\n                        },\r\n                        (error) => {\r\n                          if (error) {\r\n                            console.error(\"QR Code generation error:\", error);\r\n                            return;\r\n                          }\r\n\r\n                          // Add logo overlay\r\n                          const ctx = canvas.getContext(\"2d\");\r\n                          const logo = new Image();\r\n                          logo.crossOrigin = \"anonymous\";\r\n                          logo.onload = () => {\r\n                            // Calculate logo size and position (center of QR code)\r\n                            const logoSize = isDesktop ? 32 : 24;\r\n                            const x = (canvas.width - logoSize) / 2;\r\n                            const y = (canvas.height - logoSize) / 2;\r\n\r\n                            // Draw white background circle for logo\r\n                            ctx.fillStyle = \"#ffffff\";\r\n                            ctx.beginPath();\r\n                            ctx.arc(\r\n                              x + logoSize / 2,\r\n                              y + logoSize / 2,\r\n                              logoSize / 2 + 2,\r\n                              0,\r\n                              2 * Math.PI\r\n                            );\r\n                            ctx.fill();\r\n\r\n                            // Draw the logo\r\n                            ctx.drawImage(logo, x, y, logoSize, logoSize);\r\n                          };\r\n                          logo.onerror = () => {\r\n                            console.log(\"Logo not found, QR code generated without logo\");\r\n                          };\r\n                          logo.src = \"/assets/idigics_logo.png\";\r\n                        }\r\n                      );\r\n                    }).catch((error) => {\r\n                      console.error(\"Failed to load QR code library:\", error);\r\n                    });\r\n                  }\r\n                }}\r\n                style={{ borderRadius: \"6px\" }}\r\n              />\r\n            </Box>\r\n          </StyledQRCard>\r\n        </Box>\r\n      </Box>\r\n    </StyledContainer>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC1E,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,MAAM,QAAQ,sBAAsB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGH,MAAM,CAACP,GAAG,CAAC,CAACW,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClDE,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE,qDAAqD;IACjEC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE,MAAM;IACXC,UAAU,EAAE,sBAAsB;IAClC,CAACP,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,OAAO,EAAE,QAAQ;MACjBC,GAAG,EAAE;IACP;EACF,CAAC;AAAA,CAAC,CAAC;AAACI,EAAA,GAZEZ,eAAe;AAcrB,MAAMa,YAAY,GAAGhB,MAAM,CAACR,KAAK,CAAC,CAACyB,KAAA;EAAA,IAAC;IAAEZ;EAAM,CAAC,GAAAY,KAAA;EAAA,OAAM;IACjDC,MAAM,EAAE,MAAM;IACdC,eAAe,EAAE,0BAA0B;IAC3CC,cAAc,EAAE,WAAW;IAC3BC,YAAY,EAAE,MAAM;IACpBf,OAAO,EAAE,MAAM;IACfgB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBb,OAAO,EAAE,QAAQ;IACjBc,MAAM,EAAE,oCAAoC;IAC5CC,SAAS,EAAE,sCAAsC;IACjD,CAACpB,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BI,MAAM,EAAE,MAAM;MACdR,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC,CAAC;AAACgB,GAAA,GAfEV,YAAY;AAiBlB,MAAMW,iBAAiB,GAAG3B,MAAM,CAACR,KAAK,CAAC,CAACoC,KAAA;EAAA,IAAC;IAAEvB;EAAM,CAAC,GAAAuB,KAAA;EAAA,OAAM;IACtDT,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,MAAM;IACpBI,SAAS,EAAE,sCAAsC;IACjDf,OAAO,EAAE,MAAM;IACfJ,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBgB,UAAU,EAAE,QAAQ;IACpBM,SAAS,EAAE,QAAQ;IACnB,CAACxB,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,OAAO,EAAE,QAAQ;MACjBY,cAAc,EAAE,QAAQ;MACxBJ,MAAM,EAAE;IACV;EACF,CAAC;AAAA,CAAC,CAAC;AAACY,GAAA,GAdEH,iBAAiB;AAgBvB,MAAMI,iBAAiB,GAAG/B,MAAM,CAACR,KAAK,CAAC,CAACwC,KAAA;EAAA,IAAC;IAAE3B;EAAM,CAAC,GAAA2B,KAAA;EAAA,OAAM;IACtDb,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,MAAM;IACpBI,SAAS,EAAE,sCAAsC;IACjDf,OAAO,EAAE,MAAM;IACfJ,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvB,CAACF,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC,CAAC;AAACuB,GAAA,GAVEF,iBAAiB;AAYvB,MAAMG,YAAY,GAAGlC,MAAM,CAACR,KAAK,CAAC,CAAC2C,KAAA;EAAA,IAAC;IAAE9B;EAAM,CAAC,GAAA8B,KAAA;EAAA,OAAM;IACjDhB,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,MAAM;IACpBI,SAAS,EAAE,sCAAsC;IACjDf,OAAO,EAAE,QAAQ;IACjBJ,OAAO,EAAE,MAAM;IACfiB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxB,CAACjB,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC,CAAC;AAAC0B,GAAA,GAXEF,YAAY;AAalB,OAAO,MAAMG,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,iBAAA,EAAAC,aAAA;EACzB,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,SAAS,GAAG5C,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC;IACrC0D,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;MACPC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE,EAAE;MACVN,EAAE,EAAE,CAAC;MACLO,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,IAAI;MACbC,mBAAmB,EAAE,EAAE;MACvBC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,CAAC;MACtBC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJjB,EAAE,EAAE,CAAC;MACLkB,QAAQ,EAAE,EAAE;MACZJ,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE;IACV,CAAC;IACDI,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,CAAC;IACZC,wBAAwB,EAAE,CAAC;IAC3BC,4BAA4B,EAAE,CAAC;IAC/BC,qBAAqB,EAAE,CAAC;IACxBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAEhC,OAAO,CAACyB,wBAAwB,IAAI,CAAC;IAC5CQ,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAEhC,OAAO,CAAC0B,4BAA4B,IAAI,CAAC;IAChDO,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAEhC,OAAO,CAAC2B,qBAAqB,IAAI,CAAC;IACzCM,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAEhC,OAAO,CAAC4B,wBAAwB,IAAI,CAAC;IAC5CK,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,KAAK,EAAEhC,OAAO,CAAC6B,kBAAkB,IAAI,CAAC;IACtCI,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGzF,WAAW,CAAC,YAAY;IAC/C,MAAM0F,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,eAAe,GAAGJ,WAAW,CAACK,SAAS,CAC3CL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CACjC,CAAC;IACD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1F,mBAAmB,CAACuF,eAAe,CAAC;MAC3DtC,UAAU,CAACyC,QAAQ,CAACC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9C,QAAQ,CAAC,MAAM,CAAC;IAClB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdvD,SAAS,CAAC,MAAM;IACd2F,gBAAgB,CAAC,CAAC;IAClB;IACA,MAAMW,UAAU,GAAGC,WAAW,CAACZ,gBAAgB,EAAE,KAAK,CAAC;IACvD,OAAO,MAAMa,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,CAACX,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMc,aAAa,GAAIC,MAAM,IAAK;IAChC,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,QAAQ,GAAG,CAAC;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,QAAQ,EAAEC,CAAC,EAAE,EAAE;MAClC,MAAMC,eAAe,GAAGJ,MAAM,IAAIG,CAAC,GAAG,CAAC,CAAC;MACxC,IAAIC,eAAe,IAAI,IAAI,EAAE;QAC3BH,KAAK,CAACI,IAAI,eAAChG,OAAA;UAAWiG,SAAS,EAAC,aAAa;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAQ;QAAE,GAAxEN,CAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAyE,CAAC,CAAC;MACjG,CAAC,MAAM,IAAIT,eAAe,IAAI,IAAI,EAAE;QAClCH,KAAK,CAACI,IAAI,eAAChG,OAAA;UAAWiG,SAAS,EAAC,sBAAsB;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAQ;QAAE,GAAjFN,CAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkF,CAAC,CAAC;MAC1G,CAAC,MAAM;QACLZ,KAAK,CAACI,IAAI,eAAChG,OAAA;UAAWiG,SAAS,EAAC,aAAa;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAQ;QAAE,GAAxEN,CAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAyE,CAAC,CAAC;MACjG;IACF;IACA,OAAOZ,KAAK;EACd,CAAC;EAED,oBACE5F,OAAA,CAACC,eAAe;IAAAwG,QAAA,gBAEdzG,OAAA,CAACc,YAAY;MAAC4F,SAAS,EAAE,CAAE;MAAAD,QAAA,gBACzBzG,OAAA,CAACT,GAAG;QAACoH,EAAE,EAAE;UAAEvG,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEZ,GAAG,EAAE;QAAE,CAAE;QAAAgG,QAAA,gBACzDzG,OAAA,CAACR,UAAU;UACToH,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,IAAI9B,MAAM,CAAC+B,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;cAC7BtE,QAAQ,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,MAAM;cACLA,QAAQ,CAAC,aAAa,CAAC;YACzB;UACF,CAAE;UACFmE,EAAE,EAAE;YACFR,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE;cACTlF,eAAe,EAAE;YACnB;UACF,CAAE;UAAAwF,QAAA,eAEFzG,OAAA,CAACP,SAAS;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbxG,OAAA;UACE+G,GAAG,EAAC,0BAA0B;UAC9BC,GAAG,EAAC,cAAc;UAClBJ,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,GAAG,CAAE;UAC7B0D,KAAK,EAAE;YACLlF,MAAM,EAAEyB,SAAS,GAAG,MAAM,GAAG,MAAM;YACnCwE,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE;UACd,CAAE;UACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACqB,OAAO,GAAG,KAAM;UACpDC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACqB,OAAO,GAAG;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNxG,OAAA,CAACT,GAAG;QAACoH,EAAE,EAAE;UAAEvG,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE;QAAS;MAAE;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGfxG,OAAA,CAACT,GAAG;MAACoH,EAAE,EAAE;QACPvG,OAAO,EAAE,MAAM;QACfqH,mBAAmB,EAAEhF,SAAS,GAAG,WAAW,GAAG,KAAK;QACpDhC,GAAG,EAAEgC,SAAS,GAAG,QAAQ,GAAG,MAAM;QAClCiF,QAAQ,EAAE,CAAC;QACXpH,SAAS,EAAE;MACb,CAAE;MAAAmG,QAAA,gBAEAzG,OAAA,CAACyB,iBAAiB;QAACiF,SAAS,EAAE,CAAE;QAAAD,QAAA,gBAC9BzG,OAAA,CAACT,GAAG;UAACoH,EAAE,EAAE;YACPgB,QAAQ,EAAE,UAAU;YACpBV,KAAK,EAAExE,SAAS,GAAG,2BAA2B,GAAG,MAAM;YACvDzB,MAAM,EAAEyB,SAAS,GAAG,2BAA2B,GAAG,MAAM;YACxDmF,YAAY,EAAEnF,SAAS,GAAG,0BAA0B,GAAG;UACzD,CAAE;UAAAgE,QAAA,gBACAzG,OAAA,CAACZ,MAAM;YACL2H,GAAG,EAAErE,OAAO,CAACO,OAAO,CAACQ,cAAe;YACpCkD,EAAE,EAAE;cACFM,KAAK,EAAE,MAAM;cACbjG,MAAM,EAAE,MAAM;cACdM,MAAM,EAAEmB,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;cAC7DlB,SAAS,EAAE,4BAA4B;cACvCsG,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,QAAQ;cAC7DqF,UAAU,EAAE,GAAG;cACf7G,eAAe,EAAE,SAAS;cAC1BkF,KAAK,EAAE;YACT,CAAE;YAAAM,QAAA,EAED,CAAC/D,OAAO,CAACO,OAAO,CAACQ,cAAc,IAC9B,GAAG,EAAApB,kBAAA,GAAAK,OAAO,CAACG,SAAS,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAoB,CAAC,CAAC,KAAI,EAAE,GAAG,EAAAC,iBAAA,GAAAI,OAAO,CAACK,QAAQ,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAmB,CAAC,CAAC,KAAI,EAAE;UAAE;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CAAC,eACTxG,OAAA,CAACT,GAAG;YAACoH,EAAE,EAAE;cACPgB,QAAQ,EAAE,UAAU;cACpBI,KAAK,EAAEtF,SAAS,GAAG,MAAM,GAAG,MAAM;cAClCnB,MAAM,EAAEmB,SAAS,GAAG,6BAA6B,GAAG,6BAA6B;cACjFtB,YAAY,EAAE,KAAK;cACnBoG,OAAO,EAAE;YACX;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENxG,OAAA,CAACX,UAAU;UAACsH,EAAE,EAAE;YACdkB,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,QAAQ;YAC7DqF,UAAU,EAAE,GAAG;YACfF,YAAY,EAAE,QAAQ;YACtBzB,KAAK,EAAE;UACT,CAAE;UAAAM,QAAA,GACC/D,OAAO,CAACG,SAAS,EAAC,GAAC,EAACH,OAAO,CAACK,QAAQ;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEbxG,OAAA,CAACX,UAAU;UAACsH,EAAE,EAAE;YACdkB,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,SAAS;YAC9DqF,UAAU,EAAE,GAAG;YACf3B,KAAK,EAAE,mBAAmB;YAC1ByB,YAAY,EAAEnF,SAAS,GAAG,8BAA8B,GAAG;UAC7D,CAAE;UAAAgE,QAAA,EACC/D,OAAO,CAACO,OAAO,CAACK,UAAU,IAAI;QAAY;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEbxG,OAAA,CAACT,GAAG;UAACoH,EAAE,EAAE;YAAEqB,SAAS,EAAEvF,SAAS,GAAG,4BAA4B,GAAG;UAAS,CAAE;UAAAgE,QAAA,gBAC1EzG,OAAA,CAACX,UAAU;YAACsH,EAAE,EAAE;cACdkB,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,QAAQ;cAC7DqF,UAAU,EAAE,GAAG;cACf3B,KAAK,EAAE,mBAAmB;cAC1B8B,UAAU,EAAE,CAAC;cACbL,YAAY,EAAE;YAChB,CAAE;YAAAnB,QAAA,GACC,EAAAlE,aAAA,GAAAG,OAAO,CAACuB,IAAI,cAAA1B,aAAA,uBAAZA,aAAA,CAAc2F,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,eAClClI,OAAA,CAACX,UAAU;cAAC8I,SAAS,EAAC,MAAM;cAACxB,EAAE,EAAE;gBAC/BkB,QAAQ,EAAEpF,SAAS,GAAG,8BAA8B,GAAG,QAAQ;gBAC/DqF,UAAU,EAAE,GAAG;gBACf3B,KAAK,EAAE;cACT,CAAE;cAAAM,QAAA,EAAC;YAEH;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEbxG,OAAA,CAACT,GAAG;YAACoH,EAAE,EAAE;cACPkB,QAAQ,EAAEpF,SAAS,GAAG,8BAA8B,GAAG,QAAQ;cAC/D2D,MAAM,EAAE3D,SAAS,GAAG,8BAA8B,GAAG;YACvD,CAAE;YAAAgE,QAAA,EACCf,aAAa,CAAChD,OAAO,CAACuB,IAAI,IAAI,CAAC;UAAC;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAENxG,OAAA,CAACX,UAAU;YAACsH,EAAE,EAAE;cACdkB,QAAQ,EAAEpF,SAAS,GAAG,gCAAgC,GAAG,QAAQ;cACjE0D,KAAK,EAAE,SAAS;cAChB2B,UAAU,EAAE;YACd,CAAE;YAAArB,QAAA,GAAC,WACQ,EAAC/D,OAAO,CAACwB,SAAS,IAAI,CAAC,EAAC,UACnC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAGpBxG,OAAA,CAACT,GAAG;QAACoH,EAAE,EAAE;UACPvG,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBI,GAAG,EAAEgC,SAAS,GAAG,QAAQ,GAAG,MAAM;UAClCnC,SAAS,EAAE;QACb,CAAE;QAAAmG,QAAA,gBAEAzG,OAAA,CAAC6B,iBAAiB;UAAC6E,SAAS,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEe,QAAQ,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACnDzG,OAAA,CAACX,UAAU;YAACsH,EAAE,EAAE;cACdkB,QAAQ,EAAEpF,SAAS,GAAG,8BAA8B,GAAG,QAAQ;cAC/DqF,UAAU,EAAE,GAAG;cACfF,YAAY,EAAEnF,SAAS,GAAG,8BAA8B,GAAG,MAAM;cACjE0D,KAAK,EAAE;YACT,CAAE;YAAAM,QAAA,EAAC;UAEH;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbxG,OAAA,CAACT,GAAG;YAACoH,EAAE,EAAE;cACPvG,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBI,GAAG,EAAEgC,SAAS,GAAG,4BAA4B,GAAG,QAAQ;cACxDiF,QAAQ,EAAE,CAAC;cACXtG,cAAc,EAAE;YAClB,CAAE;YAAAqF,QAAA,EACCjC,OAAO,CAAC4D,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;cAAA,IAAAC,aAAA;cAAA,oBACzBvI,OAAA,CAACT,GAAG;gBAAaoH,EAAE,EAAE;kBACnBvG,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBZ,GAAG,EAAEgC,SAAS,GAAG,QAAQ,GAAG;gBAC9B,CAAE;gBAAAgE,QAAA,gBACAzG,OAAA,CAACT,GAAG;kBAACoH,EAAE,EAAE;oBACPM,KAAK,EAAExE,SAAS,GAAG,MAAM,GAAG,MAAM;oBAClCoF,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,MAAM;oBAC3D0D,KAAK,EAAE,mBAAmB;oBAC1BxE,SAAS,EAAE,QAAQ;oBACnB6G,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,eACAzG,OAAA;oBAAGiG,SAAS,EAAEoC,MAAM,CAAC1D;kBAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eAENxG,OAAA,CAACT,GAAG;kBAACoH,EAAE,EAAE;oBACPvG,OAAO,EAAE,MAAM;oBACfiB,UAAU,EAAE,QAAQ;oBACpBZ,GAAG,EAAEgC,SAAS,GAAG,QAAQ,GAAG,QAAQ;oBACpCgG,IAAI,EAAE,CAAC;oBACPC,QAAQ,EAAE;kBACZ,CAAE;kBAAAjC,QAAA,gBACAzG,OAAA,CAACX,UAAU;oBAACsH,EAAE,EAAE;sBACdgC,SAAS,EAAElG,SAAS,GAAG,2BAA2B,GAAG,OAAO;sBAC5DqF,UAAU,EAAE,GAAG;sBACfD,QAAQ,EAAEpF,SAAS,GAAG,+BAA+B,GAAG,SAAS;sBACjE0D,KAAK,EAAE,SAAS;sBAChByC,UAAU,EAAE,QAAQ;sBACpBC,QAAQ,EAAE,QAAQ;sBAClBC,YAAY,EAAE;oBAChB,CAAE;oBAAArC,QAAA,EACC4B,MAAM,CAAC5D;kBAAI;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAEbxG,OAAA,CAACT,GAAG;oBAACoH,EAAE,EAAE;sBACPgC,SAAS,EAAElG,SAAS,GAAG,2BAA2B,GAAG,MAAM;sBAC3DzB,MAAM,EAAEyB,SAAS,GAAG,MAAM,GAAG,KAAK;sBAClCxB,eAAe,EAAE,SAAS;sBAC1BE,YAAY,EAAEsB,SAAS,GAAG,KAAK,GAAG,KAAK;sBACvCoG,QAAQ,EAAE,QAAQ;sBAClBH,QAAQ,EAAEjG,SAAS,GAAG,MAAM,GAAG,MAAM;sBACrCkF,QAAQ,EAAE;oBACZ,CAAE;oBAAAlB,QAAA,eACAzG,OAAA,CAACT,GAAG;sBAACoH,EAAE,EAAE;wBACP3F,MAAM,EAAE,MAAM;wBACdT,UAAU,EAAE,0CAA0C;wBACtDY,YAAY,EAAEsB,SAAS,GAAG,KAAK,GAAG,KAAK;wBACvCwE,KAAK,EAAE,GAAG8B,IAAI,CAACC,GAAG,CAAEX,MAAM,CAAC3D,KAAK,GAAG,CAAC,GAAI,GAAG,EAAE,GAAG,CAAC,GAAG;wBACpDyC,UAAU,EAAE;sBACd;oBAAE;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAENxG,OAAA,CAACX,UAAU;oBAACsH,EAAE,EAAE;sBACdgC,SAAS,EAAElG,SAAS,GAAG,MAAM,GAAG,MAAM;sBACtCqF,UAAU,EAAE,GAAG;sBACfD,QAAQ,EAAEpF,SAAS,GAAG,4BAA4B,GAAG,SAAS;sBAC9D0D,KAAK,EAAE,SAAS;sBAChBxE,SAAS,EAAE,OAAO;sBAClB6G,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,EACC,EAAA8B,aAAA,GAAAF,MAAM,CAAC3D,KAAK,cAAA6D,aAAA,uBAAZA,aAAA,CAAcL,OAAO,CAAC,CAAC,CAAC,KAAI;kBAAK;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA,GA9DE8B,KAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+DV,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAGpBxG,OAAA,CAACgC,YAAY;UAAC0E,SAAS,EAAE,CAAE;UAAAD,QAAA,eACzBzG,OAAA,CAACT,GAAG;YAACoH,EAAE,EAAE;cACPM,KAAK,EAAExE,SAAS,GAAG,2BAA2B,GAAG,OAAO;cACxDzB,MAAM,EAAEyB,SAAS,GAAG,2BAA2B,GAAG,OAAO;cACzDxB,eAAe,EAAE,MAAM;cACvBT,OAAO,EAAEiC,SAAS,GAAG,KAAK,GAAG,KAAK;cAClCtB,YAAY,EAAE,MAAM;cACpBf,OAAO,EAAE,MAAM;cACfiB,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAqF,QAAA,eACAzG,OAAA;cACEiJ,GAAG,EAAGC,MAAM,IAAK;gBACf,IAAIA,MAAM,IAAIxG,OAAO,CAACO,OAAO,CAACa,QAAQ,EAAE;kBACtC;kBACA,MAAMqF,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;kBACnCD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAACjC,KAAK,EAAEiC,MAAM,CAAClI,MAAM,CAAC;kBAEhD,MAAM,CAAC,QAAQ,CAAC,CAACsI,IAAI,CAAEC,MAAM,IAAK;oBAChC,MAAMC,MAAM,GAAG/G,SAAS,GAAG,GAAG,GAAG,GAAG;oBACpCyG,MAAM,CAACjC,KAAK,GAAGuC,MAAM;oBACrBN,MAAM,CAAClI,MAAM,GAAGwI,MAAM;oBAEtBD,MAAM,CAACE,QAAQ,CACbP,MAAM,EACN,GAAGtJ,WAAW,YAAY8C,OAAO,CAACO,OAAO,CAACa,QAAQ,EAAE,EACpD;sBACEmD,KAAK,EAAEuC,MAAM;sBACbpD,MAAM,EAAE,CAAC;sBACTD,KAAK,EAAE;wBACLuD,IAAI,EAAE,SAAS;wBACfC,KAAK,EAAE;sBACT,CAAC;sBACDC,oBAAoB,EAAE,GAAG;sBACzBC,IAAI,EAAE,WAAW;sBACjBC,OAAO,EAAE,IAAI;sBACbC,YAAY,EAAE;wBACZD,OAAO,EAAE;sBACX;oBACF,CAAC,EACAxE,KAAK,IAAK;sBACT,IAAIA,KAAK,EAAE;wBACT0E,OAAO,CAAC1E,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;wBACjD;sBACF;;sBAEA;sBACA,MAAM6D,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;sBACnC,MAAMa,IAAI,GAAG,IAAIC,KAAK,CAAC,CAAC;sBACxBD,IAAI,CAACE,WAAW,GAAG,WAAW;sBAC9BF,IAAI,CAACG,MAAM,GAAG,MAAM;wBAClB;wBACA,MAAMC,QAAQ,GAAG5H,SAAS,GAAG,EAAE,GAAG,EAAE;wBACpC,MAAM6H,CAAC,GAAG,CAACpB,MAAM,CAACjC,KAAK,GAAGoD,QAAQ,IAAI,CAAC;wBACvC,MAAME,CAAC,GAAG,CAACrB,MAAM,CAAClI,MAAM,GAAGqJ,QAAQ,IAAI,CAAC;;wBAExC;wBACAlB,GAAG,CAACqB,SAAS,GAAG,SAAS;wBACzBrB,GAAG,CAACsB,SAAS,CAAC,CAAC;wBACftB,GAAG,CAACuB,GAAG,CACLJ,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAChBE,CAAC,GAAGF,QAAQ,GAAG,CAAC,EAChBA,QAAQ,GAAG,CAAC,GAAG,CAAC,EAChB,CAAC,EACD,CAAC,GAAGtB,IAAI,CAAC4B,EACX,CAAC;wBACDxB,GAAG,CAACyB,IAAI,CAAC,CAAC;;wBAEV;wBACAzB,GAAG,CAAC0B,SAAS,CAACZ,IAAI,EAAEK,CAAC,EAAEC,CAAC,EAAEF,QAAQ,EAAEA,QAAQ,CAAC;sBAC/C,CAAC;sBACDJ,IAAI,CAACa,OAAO,GAAG,MAAM;wBACnBd,OAAO,CAACe,GAAG,CAAC,gDAAgD,CAAC;sBAC/D,CAAC;sBACDd,IAAI,CAAClD,GAAG,GAAG,0BAA0B;oBACvC,CACF,CAAC;kBACH,CAAC,CAAC,CAACiE,KAAK,CAAE1F,KAAK,IAAK;oBAClB0E,OAAO,CAAC1E,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;kBACzD,CAAC,CAAC;gBACJ;cACF,CAAE;cACFY,KAAK,EAAE;gBAAE/E,YAAY,EAAE;cAAM;YAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAACpE,EAAA,CA3aWD,KAAK;EAAA,QACCxC,WAAW,EACVE,aAAa;AAAA;AAAAoL,GAAA,GAFpB9I,KAAK;AAAA,IAAAtB,EAAA,EAAAW,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAA+I,GAAA;AAAAC,YAAA,CAAArK,EAAA;AAAAqK,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}