import { useEffect, useState, useRef } from "react";
import "./Profile.css";
import { checkAuthToken } from "../AuthenticationData.ts";
import { GetProfilesFromLink } from "../ProfileData.ts";
import { toast } from "react-toastify";
import { useProfile } from "../Context/ProfileContext";
import { UseCoupon, ReserveCoupon } from "../CouponsData.ts";
import { useNavigate } from "react-router-dom";
import { PostView, PostClick } from "../AnalyticsData.ts";
import RatingDialog from "./RatingDialog";
import CheckoutReserved from "../sections/@dashboard/Coupons/CheckoutReserved";
import {
  Box,
  CircularProgress,
  Typography,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Worker, Viewer } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import CloseIcon from "@mui/icons-material/Close";
import PortraitIcon from "@mui/icons-material/Portrait";
import { Helmet } from "react-helmet-async";

const Profile = () => {
  const hasRun = useRef(false);
  const { profile, fetchProfile } = useProfile();
  const navigate = useNavigate();
  const [SerialKey, setSerialKey] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [Account, setAccount] = useState({
    email: "",
    firstName: "",
    id: 0,
    lastName: "",
    category: "",
    profile: {
      birthDate: "",
      customLinks: null,
      gender: "",
      id: 0,
      isPremium: false,
      occupation: "",
      premium: null,
      profileCoverPicture: "",
      profilePicture: "",
      profilePictureFrame: 0,
      socialLinks: null,
      user: null,
      userId: 0,
      userName: "",
      country: "",
    },
    auth: {
      id: 0,
      password: "",
      user: null,
      userId: 8,
    },
    contacts: [],
    rate: 0,
    rateCount: 0,
    rate_Skill_QualityOfWork: 0,
    rate_Skill_CostEffectiveness: 0,
    rate_Skill_Timeliness: 0,
    rate_Skill_Communication: 0,
    rate_Skill_Agility: 0,
  });

  // Component state
  const [SocialLinks, setSocialLinks] = useState([]);
  const [CustomLinks, setCustomLinks] = useState([]);
  const [sameAccount, setSameAccount] = useState(false);
  const [activeTab, setActiveTab] = useState("links");

  // Modal states
  const [openShareModal, setOpenShareModal] = useState(false);
  const [openContactModal, setOpenContactModal] = useState(false);
  const [openRatingDialog, setOpenRatingDialog] = useState(
    SerialKey ? true : false
  );
  const [openReserveDialog, setOpenReserveDialog] = useState(false);
  const [openCvDialog, setOpenCvDialog] = useState(false);
  const cvFileRef = useRef(null);

  // Tab change handler
  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const handleApplyCoupon = async (coupon) => {
    try {
      // Input validation
      if (!coupon || typeof coupon !== "string" || coupon.trim() === "") {
        throw new Error("Please enter a valid coupon serial key.");
      }

      const response = await ReserveCoupon(coupon.trim());

      if (response.error) {
        throw new Error(response.error);
      }

      // Success message
      const successMessage =
        response.data?.message || "Coupon reserved successfully!";
      toast.success(successMessage, {
        position: "top-center",
        autoClose: 2000,
      });

      return response;
    } catch (error) {
      // Enhanced error display
      const errorMessage =
        error.message || "Failed to reserve coupon. Please try again.";
      toast.error(errorMessage, {
        position: "top-center",
        autoClose: 3000,
      });

      // Return error for component handling
      return { error: errorMessage };
    }
  };

  const handleViewCount = async (profile, profileToView) => {
    if (hasRun.current) return;
    try {
      const viewData = {
        userId: profileToView.id,
        date: new Date(),
      };

      // If the user is logged in and views someone else's profile
      if (profile && profile.id && profile.id !== profileToView.id) {
        viewData.gender = profile.profile?.gender ?? null;
        viewData.country = profile.profile?.country ?? "Tunisia";
        await PostView(viewData);
        hasRun.current = true;
      }

      // If the user is not logged in and views a profile
      if (profile == null) {
        await PostView(viewData);
        hasRun.current = true;
      }
    } catch (error) {
      console.error("Error posting view:", error);
    }
  };

  useEffect(() => {
    const storedSerialKey = localStorage.getItem("serialKey");
    if (storedSerialKey) {
      setSerialKey(storedSerialKey);
      setOpenRatingDialog(true);
    }
    const fetchData = () => {
      fetchProfileData();
    };

    // Initial fetch
    fetchData();

    // Execute fetch every minute - COMMENTED OUT FOR NOW
    // const intervalId = setInterval(fetchData, 15000);

    // return () => {
    //   clearInterval(intervalId);
    // };
  }, [profile]);

  useEffect(() => {
    const runOnce = async () => {
      try {
        // Check if the user is authenticated
        const isAuthenticated = checkAuthToken();

        // If the user is authenticated, ensure their profile is fetched
        if (isAuthenticated && (!profile || !profile.id)) {
          await fetchProfile();
        }

        const currentPath = window.location.pathname;
        const searchQueryPart = currentPath.substring(
          currentPath.lastIndexOf("/") + 1
        );

        // Fetch the profile associated with the current URL
        const response = await GetProfilesFromLink(searchQueryPart);
        const profileToView = response.data;

        // Determine if the authenticated user matches the profile being viewed
        if (isAuthenticated) {
          setSameAccount(profile.id === profileToView.id);
        } else {
          setSameAccount(false);
        }

        // Handle view count logic
        await handleViewCount(isAuthenticated ? profile : null, profileToView);
      } catch (error) {
        console.error("Error in runOnce:", error);
      }
    };

    runOnce();
  }, [profile]);

  const fetchProfileData = async () => {
    const currentPath = window.location.pathname;
    const searchQueryPart = currentPath.substring(
      currentPath.lastIndexOf("/") + 1
    );
    try {
      setIsLoading(true);
      const response = await GetProfilesFromLink(searchQueryPart);
      if (response.error) {
        throw new Error(response.error);
      }

      setAccount(response.data);
      setSocialLinks(response.data.profile.socialLinks);
      setCustomLinks(response.data.profile.customLinks || []);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      navigate("/404");
    }
  };

  const handleSendRate = async (data) => {
    try {
      const response = await UseCoupon(data);
      if (response.error) throw new Error(response.error);

      localStorage.removeItem("serialKey");
      setSerialKey(null);
      toast.success("Rate sent successfully", {
        position: "top-center",
        autoClose: 1000,
      });
    } catch (error) {
      toast.error(error.message, {
        position: "top-center",
        autoClose: 1000,
      });
    }
    fetchProfile();
  };

  // Helper functions
  const generateStars = (score, maxStars = 5) => {
    let starsHTML = [];
    const fullStars = Math.floor(score);
    const halfStarThreshold = 0.4;
    const halfStar = score % 1 >= halfStarThreshold;
    const emptyStars = maxStars - fullStars - (halfStar ? 1 : 0);

    for (let i = 0; i < fullStars; i++) {
      starsHTML.push(<i key={`full-${i}`} className="fa-solid fa-star"></i>);
    }
    if (halfStar) {
      starsHTML.push(
        <i key="half" className="fa-solid fa-star-half-stroke"></i>
      );
    }
    for (let i = 0; i < emptyStars; i++) {
      starsHTML.push(<i key={`empty-${i}`} className="fa-regular fa-star"></i>);
    }

    return starsHTML;
  };

  const handleLinkClick = async (linkId, linkUrl, category) => {
    // Validate URL first
    if (!validateURL(linkUrl)) {
      navigate("/404");
      return;
    }

    // For iOS, navigate immediately to avoid popup blocking
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
      // Post analytics in background after navigation
      if (!sameAccount) {
        PostClick({
          linkId: linkId,
          userId: Account.id,
          gender: Account.profile?.gender ?? null,
          country: Account.profile?.country ?? null,
          category: category,
          date: new Date(),
        }).catch(console.error); // Don't await, just fire and forget
      }
      window.open(linkUrl, "_blank");
    } else {
      // For other devices, use the original approach with timeout
      setTimeout(async () => {
        if (!sameAccount) {
          await PostClick({
            linkId: linkId,
            userId: Account.id,
            gender: Account.profile?.gender ?? null,
            country: Account.profile?.country ?? null,
            category: category,
            date: new Date(),
          });
        }
        window.open(linkUrl, "_blank");
      }, 100);
    }
  };

  const validateURL = (url) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  const handleContactClick = (type, value) => {
    let href = "#";
    const encodedValue = encodeURIComponent(value);
    const cleanTel = value.replace(/[^0-9+]/g, "");
    const cleanWa = value.replace(/[^0-9]/g, "");

    // Add a small delay to ensure touch event is properly registered on iOS
    setTimeout(() => {
      switch (type.toLowerCase()) {
        case "tel":
        case "phone":
        case "phonenumber":
          href = `tel:${cleanTel}`;
          break;
        case "wa":
        case "whatsapp":
          href = `https://wa.me/${cleanWa}`;
          // Use a more iOS-friendly approach
          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
            window.location.href = href;
          } else {
            window.open(href, "_blank");
          }
          return;
        case "mailto":
        case "email":
          href = `mailto:${value}`;
          break;
        case "gmail":
          href = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(
            value
          )}`;
          // Use a more iOS-friendly approach
          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
            window.location.href = href;
          } else {
            window.open(href, "_blank");
          }
          return;
        case "map":
          href = `https://maps.google.com/?q=${encodedValue}`;
          // Use a more iOS-friendly approach
          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
            window.location.href = href;
          } else {
            window.open(href, "_blank");
          }
          return;
        case "sms":
          href = `sms:${cleanTel}`;
          break;
        default:
          href = "#";
          break;
      }

      if (href !== "#") {
        window.location.href = href;
      }
    }, 100); // Small delay to ensure touch event completes
  };

  const validContacts =
    Account.contacts?.filter(
      (contact) =>
        contact.isPublic &&
        !contact.contactInfo?.startsWith("data:application/pdf")
    ) || [];

  const hasValidContacts = validContacts.length > 0;

  const handleDownloadVCard = async () => {
    try {
      console.log("vCard download clicked.");

      let vCard = `BEGIN:VCARD\r\nVERSION:3.0\r\n`;
      vCard += `FN:${Account.firstName} ${Account.lastName}\r\n`;
      vCard += `N:${Account.lastName};${Account.firstName};;;\r\n`;

      if (Account.profile?.occupation) {
        vCard += `TITLE:${Account.profile.occupation}\r\n`;
      }

      // Add organization if available
      if (Account.profile?.organization) {
        vCard += `ORG:${Account.profile.organization}\r\n`;
      }

      // Handle profile picture
      if (Account.profile?.profilePicture) {
        try {
          console.log("vCard: Fetching photo:", Account.profile.profilePicture);
          const response = await fetch(Account.profile.profilePicture);
          if (response.ok) {
            const blob = await response.blob();
            const contentType = blob.type;
            const imageType = contentType.split("/")[1]?.toUpperCase();

            if (["JPEG", "JPG", "PNG", "GIF"].includes(imageType)) {
              const reader = new FileReader();
              reader.onload = function (e) {
                const base64Data = e.target.result.split(",")[1];
                vCard += `PHOTO;ENCODING=BASE64;TYPE=${imageType}:${base64Data}\r\n`;
              };
              reader.readAsDataURL(blob);
            }
          }
        } catch (error) {
          console.warn("Could not fetch profile picture for vCard:", error);
        }
      }

      // Add contact information
      Account.contacts?.forEach((contact) => {
        const cleanValue = contact.contactInfo?.trim();
        if (!cleanValue || cleanValue.startsWith("data:application/pdf"))
          return;

        const cleanTel = cleanValue.replace(/[^0-9+]/g, "");
        const cleanWa = cleanValue.replace(/[^0-9]/g, "");

        switch (contact.category?.toLowerCase()) {
          case "tel":
          case "phone":
          case "phonenumber":
            vCard += `TEL;TYPE=WORK,VOICE:${cleanTel}\r\n`;
            break;
          case "mailto":
          case "email":
          case "gmail":
            vCard += `EMAIL;TYPE=WORK:${cleanValue}\r\n`;
            break;
          case "map":
          case "location":
          case "address":
            vCard += `ADR;TYPE=WORK:;;${cleanValue.replace(
              /\n/g,
              "\\n"
            )};;;;\r\n`;
            vCard += `URL;TYPE=Map:https://maps.google.com/?q=${encodeURIComponent(
              cleanValue
            )}\r\n`;
            break;
          case "wa":
          case "whatsapp":
            vCard += `URL;TYPE=WhatsApp:https://wa.me/${cleanWa}\r\n`;
            break;
          case "sms":
            vCard += `TEL;TYPE=CELL,SMS:${cleanTel}\r\n`;
            break;
          case "website":
            vCard += `URL:${cleanValue}\r\n`;
            break;
        }
      });

      // Add social links
      SocialLinks?.forEach((link) => {
        if (link.linkUrl?.trim()) {
          vCard += `URL:${link.linkUrl.trim()}\r\n`;
        }
      });

      // Add custom links
      CustomLinks?.forEach((link) => {
        if (link.linkUrl?.trim()) {
          vCard += `URL:${link.linkUrl.trim()}\r\n`;
        }
      });

      // Add current profile URL
      vCard += `URL:${window.location.href}\r\n`;

      // Add note
      const note = `Digital business card from iDigics.com - Connect with ${Account.firstName} ${Account.lastName}`;
      vCard += `NOTE:${note}\r\n`;

      // Add revision date and end
      vCard += `REV:${new Date().toISOString().replace(/[-:.]/g, "")}Z\r\n`;
      vCard += `END:VCARD`;

      console.log("Generated vCard:\n", vCard);

      // Create and download the vCard file
      const blob = new Blob([vCard], { type: "text/vcard;charset=utf-8" });
      const url = URL.createObjectURL(blob);
      const linkElement = document.createElement("a");
      linkElement.href = url;

      const safeFilename = `${Account.firstName}-${Account.lastName}-contact`
        .toLowerCase()
        .replace(/[^a-z0-9\-]/g, "_");
      linkElement.download = `${safeFilename}.vcf`;

      document.body.appendChild(linkElement);
      linkElement.click();
      document.body.removeChild(linkElement);
      URL.revokeObjectURL(url);

      console.log("vCard download triggered.");

      toast.success("Contact card downloaded successfully!", {
        position: "top-center",
        autoClose: 2000,
      });
    } catch (error) {
      console.error("Error generating vCard:", error);
      toast.error("Failed to generate contact card", {
        position: "top-center",
        autoClose: 2000,
      });
    }
  };

  return (
    <>
      <Helmet>
        <title>{`${Account.firstName} ${Account.lastName} | IDigics Profile`}</title>
        <meta
          name="description"
          content={`Connect with ${Account.firstName} ${Account.lastName}${
            Account.profile?.occupation ? `, ${Account.profile.occupation}` : ""
          }. View their professional profile and social links.`}
        />

        {/* Open Graph meta tags */}
        <meta
          property="og:title"
          content={`${Account.firstName} ${Account.lastName} | IDigics Profile`}
        />
        <meta
          property="og:description"
          content={`Connect with ${Account.firstName} ${Account.lastName}${
            Account.profile?.occupation ? `, ${Account.profile.occupation}` : ""
          }. View their professional profile and social links.`}
        />
        <meta property="og:type" content="profile" />
        <meta property="og:url" content={window.location.href} />
        {Account.profile?.profilePicture && (
          <meta property="og:image" content={Account.profile.profilePicture} />
        )}
        <meta property="og:site_name" content="IDigics" />

        {/* Twitter Card meta tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content={`${Account.firstName} ${Account.lastName} | IDigics Profile`}
        />
        <meta
          name="twitter:description"
          content={`Connect with ${Account.firstName} ${Account.lastName}${
            Account.profile?.occupation ? `, ${Account.profile.occupation}` : ""
          }. View their professional profile and social links.`}
        />
        {Account.profile?.profilePicture && (
          <meta name="twitter:image" content={Account.profile.profilePicture} />
        )}

        {/* Additional profile-specific meta tags */}
        <meta property="profile:first_name" content={Account.firstName} />
        <meta property="profile:last_name" content={Account.lastName} />
        {Account.profile?.userName && (
          <meta
            property="profile:username"
            content={Account.profile.userName}
          />
        )}
      </Helmet>

      <div
        className={`profile-page-body ${
          Account.profile?.profileCoverPicture ? "has-cover" : ""
        }`}
        style={{
          backgroundImage: Account.profile?.profileCoverPicture
            ? `url(${Account.profile.profileCoverPicture})`
            : "none",
        }}
      >
        {isLoading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "100vh",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <CircularProgress size={60} />
            <Typography variant="h6" color="textSecondary">
              Loading profile...
            </Typography>
          </Box>
        ) : (
          <div className="profile-container">
            {/* Profile Header Section */}
            <div className="profile-header">
              <img
                src={
                  Account.profile.profilePicture ||
                  `https://via.placeholder.com/150/CCCCCC/FFFFFF?text=${
                    Account.firstName?.charAt(0) || ""
                  }${Account.lastName?.charAt(0) || ""}`
                }
                alt="Profile"
                className="profile-pic"
              />
              <h1 className="full-name">
                {Account.firstName} {Account.lastName}
              </h1>
              <p className="occupation">{Account.profile.occupation}</p>

              {/* Contact Exchange Button */}
              <button
                className="contact-exchange-button"
                onClick={() => setOpenContactModal(true)}
                aria-label="Contact Exchange"
                title="Contact Exchange"
              >
                <i className="fas fa-user-plus"></i>
              </button>

              {/* Share Button */}
              <button
                className="share-button"
                onClick={() => setOpenShareModal(true)}
                aria-label="Share Profile"
              >
                <i className="fas fa-share-alt"></i>
              </button>
            </div>

            {/* Tab Navigation Buttons */}
            <div className="tab-buttons">
              <button
                className={`tab-button ${
                  activeTab === "links" ? "active" : ""
                }`}
                onClick={() => handleTabChange("links")}
              >
                <i className="fas fa-link"></i>Links
              </button>
              <button
                className={`tab-button ${
                  activeTab === "about" ? "active" : ""
                }`}
                onClick={() => handleTabChange("about")}
              >
                <i className="fas fa-user"></i>About
              </button>
            </div>

            {/* Links Tab Content */}
            <div
              className={`tab-content ${activeTab === "links" ? "active" : ""}`}
            >
              <div className="section">
                <h3>Connect With Me</h3>
                <ul className="links-list">
                  {SocialLinks?.filter(
                    (link) =>
                      link.category &&
                      ![
                        "tel",
                        "phone",
                        "phonenumber",
                        "wa",
                        "whatsapp",
                        "mailto",
                        "email",
                        "sms",
                        "map",
                        "location",
                        "address",
                      ].includes(link.category.toLowerCase())
                  ).map((link) => (
                    <li key={link.id} className="link-item">
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleLinkClick(link.id, link.linkUrl, link.category);
                        }}
                      >
                        <i className={getSocialIcon(link.category)}></i>
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Custom Links Section */}
              {CustomLinks && CustomLinks.length > 0 && (
                <div className="section">
                  <h3>My Links</h3>
                  <ul className="links-list">
                    {CustomLinks.map((link) => (
                      <li key={link.id} className="link-item">
                        <a
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handleLinkClick(link.id, link.linkUrl, "custom");
                          }}
                        >
                          {link.icon ? (
                            <img
                              src={link.icon}
                              alt={link.title}
                              style={{
                                width: "20px",
                                height: "20px",
                                marginRight: "15px",
                                borderRadius: "3px",
                              }}
                            />
                          ) : (
                            <i className="fas fa-link"></i>
                          )}
                          {link.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {hasValidContacts && (
                <div className="section">
                  <h3>Get In Touch</h3>
                  <ul className="links-list">
                    {Account.contacts
                      ?.filter(
                        (contact) =>
                          contact.isPublic &&
                          !contact.contactInfo?.startsWith(
                            "data:application/pdf"
                          )
                      )
                      .map((contact) => (
                        <li key={contact.id} className="link-item">
                          <button
                            className="contact-button"
                            onClick={() => {
                              handleContactClick(
                                contact.category,
                                contact.contactInfo
                              );
                            }}
                          >
                            <i className={getContactIcon(contact.category)}></i>
                            {getContactLabel(
                              contact.category,
                              contact.contactInfo,
                              contact.title
                            )}
                          </button>
                        </li>
                      ))}
                  </ul>
                </div>
              )}

              <div className="section">
                <button className="vcard-button" onClick={handleDownloadVCard}>
                  <i className="fas fa-address-card"></i>
                  Save Contact (vCard)
                </button>
              </div>
            </div>

            {/* About Tab Content */}
            <div
              className={`tab-content ${activeTab === "about" ? "active" : ""}`}
            >
              <div className="section">
                <div className="about-content">
                  <p>
                    <strong>Professional:</strong>{" "}
                    {Account.profile.occupation || "Not specified"}
                  </p>
                  {Account.profile.country && (
                    <p>
                      <strong>Location:</strong> {Account.profile.country}
                    </p>
                  )}
                  {Account.category && (
                    <p>
                      <strong>Category:</strong> {Account.category}
                    </p>
                  )}
                  <p>
                    Welcome to my profile! I'm {Account.firstName}{" "}
                    {Account.lastName},
                    {Account.profile.occupation &&
                      ` working as a ${Account.profile.occupation}`}
                    {Account.profile.country &&
                      ` based in ${Account.profile.country}`}
                    . Feel free to connect with me through the links above or
                    get in touch directly.
                  </p>
                </div>
              </div>

              {/* CV Section for Students */}
              {Account.category === "Student" && (
                <div className="section">
                  <h3>Curriculum Vitae</h3>
                  {(() => {
                    const cvContact = Account.contacts?.find(
                      (contact) =>
                        contact.category === "CvFile" && contact.isPublic
                    );

                    if (cvContact && cvContact.contactInfo) {
                      return (
                        <div className="cv-section">
                          <div className="cv-preview">
                            <div className="cv-header">
                              <i className="fas fa-file-pdf cv-icon"></i>
                              <div className="cv-info">
                                <h4>{cvContact.title || "Curriculum V"}</h4>
                                <p>Click to view or download the CV</p>
                              </div>
                            </div>
                            <div className="cv-buttons">
                              <button
                                className="cv-button cv-preview-button"
                                onClick={() => {
                                  cvFileRef.current = cvContact.contactInfo;
                                  setOpenCvDialog(true);
                                }}
                              >
                                <i className="fas fa-eye"></i>
                                Preview CV
                              </button>
                              <button
                                className="cv-button cv-download-button"
                                onClick={() => {
                                  const link = document.createElement("a");
                                  link.href = cvContact.contactInfo;
                                  link.download = `${Account.firstName}_${Account.lastName}_CV.pdf`;
                                  link.target = "_blank";
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                <i className="fas fa-download"></i>
                                Download CV
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    } else {
                      return (
                        <div className="cv-section">
                          <p className="no-cv-message">
                            <i className="fas fa-info-circle"></i>
                            No CV available at the moment.
                          </p>
                        </div>
                      );
                    }
                  })()}
                </div>
              )}

              {/* Profile Overview - Hidden for Students and Free users */}
              {Account.category !== "Student" && Account.category !== "Free" && (
                <div className="section">
                  <h3>Profile Overview</h3>
                  <div className="rating">
                    <h4>Global Rating</h4>
                    <div className="stars">{generateStars(Account.rate)}</div>
                    <span id="globalRatingText">
                      {Account.rate.toFixed(1)} out of 5 ({Account.rateCount}{" "}
                      reviews)
                    </span>

                    {!sameAccount && (
                      <div className="rating-actions">
                        <button
                          className="rating-action-button"
                          onClick={() => setOpenRatingDialog(true)}
                        >
                          <i className="fas fa-star"></i>Rate Me
                        </button>
                        <button
                          className="rating-action-button"
                          onClick={() => setOpenReserveDialog(true)}
                        >
                          <i className="fas fa-ticket-alt"></i>Reserve Coupon
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Detailed Skills - Hidden for Students */}
              {Account.category !== "Student" && (
                <div className="section">
                  <h3>Detailed Skills</h3>
                  <div className="detailed-skills">
                    <ul>
                      <li>
                        <div className="skill-item">
                          <i className="fas fa-star"></i>
                          <span>Quality of Work</span>
                        </div>
                        <div className="stars">
                          {generateStars(Account.rate_Skill_QualityOfWork)}
                        </div>
                      </li>
                      <li>
                        <div className="skill-item">
                          <i className="fas fa-hand-holding-usd"></i>
                          <span>Cost Effectiveness</span>
                        </div>
                        <div className="stars">
                          {generateStars(Account.rate_Skill_CostEffectiveness)}
                        </div>
                      </li>
                      <li>
                        <div className="skill-item">
                          <i className="fas fa-hourglass-half"></i>
                          <span>Timeliness</span>
                        </div>
                        <div className="stars">
                          {generateStars(Account.rate_Skill_Timeliness)}
                        </div>
                      </li>
                      <li>
                        <div className="skill-item">
                          <i className="fas fa-comments"></i>
                          <span>Communication</span>
                        </div>
                        <div className="stars">
                          {generateStars(Account.rate_Skill_Communication)}
                        </div>
                      </li>
                      <li>
                        <div className="skill-item">
                          <i className="fas fa-rocket"></i>
                          <span>Agility</span>
                        </div>
                        <div className="stars">
                          {generateStars(Account.rate_Skill_Agility)}
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              )}

              {/* QR Code Section */}
              <div className="section">
                <h3>Quick Connect</h3>
                <div className="qr-section">
                  <div id="profile-qrcode">
                    <canvas
                      ref={(canvas) => {
                        if (canvas && Account.profile.userName) {
                          // Clear any existing content
                          const ctx = canvas.getContext("2d");
                          ctx.clearRect(0, 0, canvas.width, canvas.height);

                          import("qrcode")
                            .then((QRCode) => {
                              QRCode.toCanvas(
                                canvas,
                                window.location.href,
                                {
                                  width: 150,
                                  margin: 1,
                                  color: {
                                    dark: "#2c3e50",
                                    light: "#ffffff",
                                  },
                                  errorCorrectionLevel: "M",
                                  type: "image/png",
                                  quality: 0.92,
                                  rendererOpts: {
                                    quality: 0.92,
                                  },
                                },
                                (error) => {
                                  if (error) {
                                    console.error(
                                      "QR Code generation error:",
                                      error
                                    );
                                    return;
                                  }

                                  // Add logo overlay
                                  const ctx = canvas.getContext("2d");
                                  const logo = new Image();
                                  logo.crossOrigin = "anonymous";
                                  logo.onload = () => {
                                    // Calculate logo size and position (center of QR code)
                                    const logoSize = 30;
                                    const x = (canvas.width - logoSize) / 2;
                                    const y = (canvas.height - logoSize) / 2;

                                    // Draw white background circle for logo
                                    ctx.fillStyle = "#ffffff";
                                    ctx.beginPath();
                                    ctx.arc(
                                      x + logoSize / 2,
                                      y + logoSize / 2,
                                      logoSize / 2 + 2,
                                      0,
                                      2 * Math.PI
                                    );
                                    ctx.fill();

                                    // Draw the logo
                                    ctx.drawImage(
                                      logo,
                                      x,
                                      y,
                                      logoSize,
                                      logoSize
                                    );
                                  };
                                  logo.onerror = () => {
                                    console.log(
                                      "Logo not found, QR code generated without logo"
                                    );
                                  };
                                  logo.src = "/assets/idigics_logo.png";
                                }
                              );
                            })
                            .catch((error) => {
                              console.error(
                                "Failed to load QR code library:",
                                error
                              );
                            });
                        }
                      }}
                    />
                  </div>
                  <p className="qr-description">
                    Scan this QR code to quickly access my profile on any device
                  </p>
                </div>
              </div>
            </div>

            {/* Share Modal */}
            {openShareModal && (
              <div
                className={`modal ${openShareModal ? "is-open" : ""}`}
                id="share-modal"
              >
                <div className="modal-content">
                  <button
                    className="close-button"
                    onClick={() => setOpenShareModal(false)}
                  >
                    ×
                  </button>
                  <h3>Share Profile</h3>
                  <div className="share-options">
                    <a
                      href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                        window.location.href
                      )}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      title="Share on Facebook"
                    >
                      <i className="fab fa-facebook-f"></i>
                    </a>
                    <a
                      href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                        window.location.href
                      )}&text=${encodeURIComponent(
                        `Check out ${Account.firstName} ${Account.lastName}'s profile!`
                      )}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      title="Share on Twitter"
                    >
                      <i className="fab fa-twitter"></i>
                    </a>
                  </div>
                  <div id="qrcode">
                    <canvas
                      ref={(canvas) => {
                        if (canvas && openShareModal) {
                          // Clear any existing content
                          const ctx = canvas.getContext("2d");
                          ctx.clearRect(0, 0, canvas.width, canvas.height);

                          import("qrcode")
                            .then((QRCode) => {
                              QRCode.toCanvas(
                                canvas,
                                window.location.href,
                                {
                                  width: 150,
                                  margin: 1,
                                  color: {
                                    dark: "#2c3e50",
                                    light: "#ffffff",
                                  },
                                  errorCorrectionLevel: "M",
                                  type: "image/png",
                                  quality: 0.92,
                                  rendererOpts: {
                                    quality: 0.92,
                                  },
                                },
                                (error) => {
                                  if (error) {
                                    console.error(
                                      "QR Code generation error:",
                                      error
                                    );
                                    return;
                                  }

                                  // Add logo overlay
                                  const ctx = canvas.getContext("2d");
                                  const logo = new Image();
                                  logo.crossOrigin = "anonymous";
                                  logo.onload = () => {
                                    // Calculate logo size and position (center of QR code)
                                    const logoSize = 30;
                                    const x = (canvas.width - logoSize) / 2;
                                    const y = (canvas.height - logoSize) / 2;

                                    // Draw white background circle for logo
                                    ctx.fillStyle = "#ffffff";
                                    ctx.beginPath();
                                    ctx.arc(
                                      x + logoSize / 2,
                                      y + logoSize / 2,
                                      logoSize / 2 + 2,
                                      0,
                                      2 * Math.PI
                                    );
                                    ctx.fill();

                                    // Draw the logo
                                    ctx.drawImage(
                                      logo,
                                      x,
                                      y,
                                      logoSize,
                                      logoSize
                                    );
                                  };
                                  logo.onerror = () => {
                                    console.log(
                                      "Logo not found, QR code generated without logo"
                                    );
                                  };
                                  logo.src = "/assets/idigics_logo.png";
                                }
                              );
                            })
                            .catch((error) => {
                              console.error(
                                "Failed to load QR code library:",
                                error
                              );
                            });
                        }
                      }}
                    />
                  </div>
                  <button
                    id="download-qr"
                    onClick={() => {
                      import("qrcode")
                        .then((QRCode) => {
                          // Create a temporary canvas for download
                          const tempCanvas = document.createElement("canvas");
                          QRCode.toCanvas(
                            tempCanvas,
                            window.location.href,
                            {
                              width: 300,
                              margin: 2,
                              color: {
                                dark: "#2c3e50",
                                light: "#ffffff",
                              },
                              errorCorrectionLevel: "M",
                              type: "image/png",
                              quality: 0.92,
                              rendererOpts: {
                                quality: 0.92,
                              },
                            },
                            (error) => {
                              if (error) {
                                console.error(
                                  "QR Code download generation error:",
                                  error
                                );
                                return;
                              }

                              // Add logo overlay to download version
                              const ctx = tempCanvas.getContext("2d");
                              const logo = new Image();
                              logo.crossOrigin = "anonymous";
                              logo.onload = () => {
                                const logoSize = 60; // Larger for download version
                                const x = (tempCanvas.width - logoSize) / 2;
                                const y = (tempCanvas.height - logoSize) / 2;

                                // Draw white background circle for logo
                                ctx.fillStyle = "#ffffff";
                                ctx.beginPath();
                                ctx.arc(
                                  x + logoSize / 2,
                                  y + logoSize / 2,
                                  logoSize / 2 + 4,
                                  0,
                                  2 * Math.PI
                                );
                                ctx.fill();

                                // Draw the logo
                                ctx.drawImage(logo, x, y, logoSize, logoSize);

                                // Download the canvas
                                const url = tempCanvas.toDataURL(
                                  "image/png",
                                  0.92
                                );
                                const link = document.createElement("a");
                                link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;
                                link.href = url;
                                link.click();
                              };
                              logo.onerror = () => {
                                // Fallback: download without logo
                                const url = tempCanvas.toDataURL(
                                  "image/png",
                                  0.92
                                );
                                const link = document.createElement("a");
                                link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;
                                link.href = url;
                                link.click();
                              };
                              logo.src = "/assets/idigics_logo.png";
                            }
                          );
                        })
                        .catch((error) => {
                          console.error(
                            "Failed to load QR code library for download:",
                            error
                          );
                        });
                    }}
                  >
                    <i className="fas fa-download"></i> Download QR
                  </button>
                </div>
              </div>
            )}

            {/* Contact Modal */}
            {openContactModal && (
              <div
                className={`modal ${openContactModal ? "is-open" : ""}`}
                id="contact-modal"
              >
                <div className="modal-content">
                  <button
                    className="close-button"
                    onClick={() => setOpenContactModal(false)}
                  >
                    ×
                  </button>
                  <h3>Contact Exchange</h3>
                  <div className="contact-options">
                    <p>
                      Exchange contact information with {Account.firstName}{" "}
                      {Account.lastName}
                    </p>
                    <div className="contact-actions">
                      <button
                        className="contact-action-button"
                        onClick={handleDownloadVCard}
                      >
                        <i className="fas fa-address-card"></i>
                        Save Contact (vCard)
                      </button>
                      <button
                        className="contact-action-button"
                        onClick={async () => {
                          try {
                            if (
                              navigator.clipboard &&
                              navigator.clipboard.writeText
                            ) {
                              await navigator.clipboard.writeText(
                                window.location.href
                              );
                              toast.success(
                                "Profile link copied to clipboard!",
                                {
                                  position: "top-center",
                                  autoClose: 2000,
                                }
                              );
                            } else {
                              // Fallback for browsers that don't support clipboard API
                              const textArea =
                                document.createElement("textarea");
                              textArea.value = window.location.href;
                              document.body.appendChild(textArea);
                              textArea.select();
                              document.execCommand("copy");
                              document.body.removeChild(textArea);
                              toast.success(
                                "Profile link copied to clipboard!",
                                {
                                  position: "top-center",
                                  autoClose: 2000,
                                }
                              );
                            }
                          } catch (error) {
                            console.error(
                              "Failed to copy to clipboard:",
                              error
                            );
                            toast.error(
                              "Failed to copy link. Please copy manually: " +
                                window.location.href,
                              {
                                position: "top-center",
                                autoClose: 5000,
                              }
                            );
                          }
                        }}
                      >
                        <i className="fas fa-link"></i>
                        Copy Profile Link
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Rating Dialog */}
            {openRatingDialog && (
              <RatingDialog
                onClose={() => setOpenRatingDialog(false)}
                onClick={handleSendRate}
                openDialog={openRatingDialog}
                serialKey={SerialKey}
              />
            )}

            {/* Reserve Dialog */}
            {openReserveDialog && (
              <CheckoutReserved
                onApply={handleApplyCoupon}
                ShowCouponSection={true}
                onClose={() => setOpenReserveDialog(false)}
              />
            )}

            {/* CV Preview Dialog */}
            {openCvDialog && (
              <Dialog
                open={openCvDialog}
                onClose={() => setOpenCvDialog(false)}
                fullWidth
                maxWidth="md"
              >
                <DialogTitle>
                  CV Preview <PortraitIcon />
                </DialogTitle>
                <DialogContent>
                  <IconButton
                    sx={{
                      position: "absolute",
                      right: 8,
                      top: 8,
                    }}
                    aria-label="close"
                    onClick={() => setOpenCvDialog(false)}
                  >
                    <CloseIcon />
                  </IconButton>
                  <div
                    style={{
                      height: "600px",
                      width: "100%",
                      overflow: "auto",
                    }}
                  >
                    <Worker
                      workerUrl={`https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`}
                    >
                      <Viewer
                        fileUrl={cvFileRef.current}
                        showPreviousViewOnLoad={false}
                      />
                    </Worker>
                  </div>
                </DialogContent>
              </Dialog>
            )}

          </div>
        )}
      </div>
    </>
  );
};

// Helper functions for icons and labels
const getSocialIcon = (category) => {
  switch (category?.toLowerCase()) {
    case "twitter":
      return "fab fa-twitter";
    case "github":
      return "fab fa-github";
    case "instagram":
      return "fab fa-instagram";
    case "facebook":
      return "fab fa-facebook-f";
    case "linkedin":
      return "fab fa-linkedin-in";
    case "youtube":
      return "fab fa-youtube";
    case "tiktok":
      return "fab fa-tiktok";
    case "snapchat":
      return "fab fa-snapchat-ghost";
    case "pinterest":
      return "fab fa-pinterest";
    case "reddit":
      return "fab fa-reddit";
    case "discord":
      return "fab fa-discord";
    case "telegram":
      return "fab fa-telegram";
    case "whatsapp":
      return "fab fa-whatsapp";
    case "website":
    case "blog":
      return "fas fa-globe";
    case "portfolio":
      return "fas fa-briefcase";
    case "email":
      return "fas fa-envelope";
    default:
      return "fas fa-link";
  }
};

const getContactIcon = (category) => {
  switch (category?.toLowerCase()) {
    case "tel":
    case "phone":
      return "fas fa-phone";
    case "wa":
    case "whatsapp":
      return "fab fa-whatsapp";
    case "mailto":
    case "email":
    case "gmail":
      return "fas fa-envelope";
    case "map":
    case "location":
    case "address":
      return "fas fa-map-marker-alt";
    case "sms":
      return "fas fa-comment-sms";
    case "fax":
      return "fas fa-fax";
    case "website":
      return "fas fa-globe";
    default:
      return "fas fa-address-book";
  }
};

const getContactLabel = (category, contactInfo, title) => {
  if (!contactInfo || contactInfo.startsWith("data:application/pdf")) {
    return "";
  }
  // If we have a title, display it along with the contact info
  if (title && title.trim()) {
    return `${title}: ${contactInfo}`;
  }
  switch (category?.toLowerCase()) {
    case "tel":
    case "phone":
    case "phonenumber":
      return contactInfo;
    case "wa":
    case "whatsapp":
      return contactInfo;
    case "mailto":
    case "email":
    case "gmail":
      return contactInfo;
    case "map":
    case "location":
    case "address":
      return contactInfo;
    case "sms":
      return contactInfo;
    case "fax":
      return contactInfo;
    case "website":
      return contactInfo;
    default:
      return contactInfo;
  }
};

export default Profile;
