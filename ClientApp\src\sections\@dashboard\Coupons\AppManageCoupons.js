import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { ToastContainer, toast } from "react-toastify";
import { Grid } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { DataGrid } from "@mui/x-data-grid";
import CheckoutReserved from "./CheckoutReserved";
import {
  Card,
  Box,
  CardHeader,
  CardContent,
  useTheme,
  Container,
  Typography,
  Stack,
  Avatar,
  Button,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

import { GetCustomerCoupons, ReserveCoupon } from "../../../CouponsData.ts";
import { useEffect } from "react";
import { styled } from "@mui/material/styles";

import SimpleBar from "simplebar-react";

const Scrollbar = styled(SimpleBar)``;

const AvailableTableColumns = [
  {
    field: "ownerDetails",
    headerName: "Owner",
    width: 250,
    renderCell: (params) => (
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={() => {
          window.open(`/Profile/${params.row.ownerUserName}`, "_blank");
        }}
      >
        <Avatar
          alt={params.row.ownerFirstName}
          src={params.row.ownerProfilePicture}
        />
        <div>
          <Typography variant="subtitle2">
            {params.row.ownerFirstName} {params.row.ownerLastName}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",
              mt: 0.5,
              display: "block",
            }}
          >
            {params.row.ownerUserName}
          </Typography>
        </div>
      </Stack>
    ),
  },
  // { field: "reference", headerName: "Reference", width: 140 },
  { field: "serialKey", headerName: "Serial Key", width: 170 },
];

export const AppManageCoupons = () => {
  const [CustomerReservedCoupons, setCustomerReservedCoupons] = useState([]);
  const [CustomerUsedCoupons, setCustomerUsedCoupons] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchCustomerCoupons();
  }, []);

  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 5,
  });

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleApplyCoupon = async (coupon) => {
    try {
      // Input validation
      if (!coupon || typeof coupon !== "string" || coupon.trim() === "") {
        throw new Error("Please enter a valid coupon serial key.");
      }

      const response = await ReserveCoupon(coupon.trim());

      if (response.error) {
        throw new Error(response.error);
      }

      // Success - refresh data
      fetchCustomerCoupons();

      // Close dialog
      setIsDialogOpen(false);

      // Show success message
      const successMessage =
        response.data?.message || "Coupon reserved successfully!";
      toast.success(successMessage, {
        position: "top-center",
        autoClose: 2000,
      });
    } catch (error) {
      const errorMessage =
        error.message || "Failed to reserve coupon. Please try again.";
      toast.error(errorMessage, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  const fetchCustomerCoupons = async () => {
    try {
      const response = await GetCustomerCoupons();

      const CustomerReservedCoupons = response.filter(
        (coupon) => coupon.isReserved && !coupon.isUsed
      );
      const CustomerUsedCoupons = response.filter(
        (coupon) => coupon.isReserved && coupon.isUsed
      );

      if (CustomerReservedCoupons.length > 0) {
        setCustomerReservedCoupons(CustomerReservedCoupons);
      }
      if (CustomerUsedCoupons.length > 0) {
        setCustomerUsedCoupons(CustomerUsedCoupons);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const theme = useTheme();

  return (
    <Container>
      <Helmet>
        <title> IDigics | Manage Coupons </title>
      </Helmet>
      <Typography variant="h5" sx={{ mb: 4 }}>
        Manage Coupons
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={12} md={8}>
          <Card>
            <CardHeader
              title="Your Reserved Coupons"
              subheader="Craft custom links to suit your needs You can easily add, edit, and delete links, logos, and URLs according to your preferences."
            />
            <CardContent>
              <Scrollbar>
                <Box sx={{ height: 370 }}>
                  <DataGrid
                    columns={AvailableTableColumns}
                    rows={CustomerReservedCoupons}
                    onRowClick={(o) => {
                      localStorage.setItem("serialKey", o.row.serialKey);
                      navigate(`/Profile/${o.row.ownerUserName}`);
                    }}
                    sx={{
                      "& .MuiDataGrid-cell:focus": {
                        outline: "none",
                      },
                      "& .MuiDataGrid-cell": {
                        alignContent: "center",
                      },
                      border: "none",
                      backgroundColor: theme.palette.common.white,
                    }}
                    paginationModel={pagination}
                    paginationMode="client"
                    onPaginationModelChange={setPagination}
                  />
                </Box>
              </Scrollbar>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={12} md={4}>
          <Card>
            <CardHeader
              title="Reserve Coupon"
              subheader="Apply a coupon code to reserve it for your use"
            />
            <CardContent>
              <Button
                fullWidth
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleOpenDialog}
                sx={{ py: 2 }}
              >
                Add Coupon
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <ToastContainer />
      </Grid>

      {/* Coupon Dialog */}
      {isDialogOpen && (
        <CheckoutReserved
          onApply={handleApplyCoupon}
          ShowCouponSection={true}
          onClose={handleCloseDialog}
        />
      )}
    </Container>
  );
};

export default AppManageCoupons;
