{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\pages\\\\Page404.js\",\n  _s = $RefreshSig$();\nimport { useNavigate } from \"react-router-dom\";\n// material\nimport { styled } from \"@mui/material/styles\";\nimport { Box, Button, Typography, Container } from \"@mui/material\";\n// components\nimport PageNotFoundIllustration from \"../components/PageNotFoundIllustration\";\nimport { Helmet } from \"react-helmet-async\";\n\n// ----------------------------------------------------------------------\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RootStyle = styled(Box)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    display: \"flex\",\n    minHeight: \"100%\",\n    alignItems: \"center\",\n    paddingTop: theme.spacing(15),\n    paddingBottom: theme.spacing(10)\n  };\n});\n\n// ----------------------------------------------------------------------\n_c = RootStyle;\nexport default function Page404() {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: /*#__PURE__*/_jsxDEV(\"title\", {\n        children: \" Page Not Found \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RootStyle, {\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxWidth: 480,\n            margin: \"auto\",\n            textAlign: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            paragraph: true,\n            color: \"primary\",\n            children: \"Sorry, page not found!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: \"text.secondary\"\n            },\n            children: \"Sorry, we couldn\\u2019t find the page you\\u2019re looking for. Perhaps you\\u2019ve mistyped the URL? Be sure to check your spelling.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PageNotFoundIllustration, {\n            sx: {\n              height: 260,\n              my: {\n                xs: 5,\n                sm: 10\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            variant: \"contained\",\n            onClick: () => {\n              navigate(-1);\n            },\n            children: \"Go Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Page404, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c2 = Page404;\nvar _c, _c2;\n$RefreshReg$(_c, \"RootStyle\");\n$RefreshReg$(_c2, \"Page404\");", "map": {"version": 3, "names": ["useNavigate", "styled", "Box", "<PERSON><PERSON>", "Typography", "Container", "PageNotFoundIllustration", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RootStyle", "_ref", "theme", "display", "minHeight", "alignItems", "paddingTop", "spacing", "paddingBottom", "_c", "Page404", "_s", "navigate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "max<PERSON><PERSON><PERSON>", "margin", "textAlign", "variant", "paragraph", "color", "height", "my", "xs", "sm", "size", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/pages/Page404.js"], "sourcesContent": ["import { useNavigate } from \"react-router-dom\";\r\n// material\r\nimport { styled } from \"@mui/material/styles\";\r\nimport { Box, Button, Typography, Container } from \"@mui/material\";\r\n// components\r\nimport PageNotFoundIllustration from \"../components/PageNotFoundIllustration\";\r\nimport { Helmet } from \"react-helmet-async\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst RootStyle = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  minHeight: \"100%\",\r\n  alignItems: \"center\",\r\n  paddingTop: theme.spacing(15),\r\n  paddingBottom: theme.spacing(10),\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function Page404() {\r\n  const navigate = useNavigate();\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title> Page Not Found </title>\r\n      </Helmet>\r\n      <RootStyle>\r\n        <Container>\r\n          <Box\r\n            sx={{\r\n              maxWidth: 480,\r\n              margin: \"auto\",\r\n              textAlign: \"center\",\r\n            }}\r\n          >\r\n            <Typography variant=\"h3\" paragraph color=\"primary\">\r\n              Sorry, page not found!\r\n            </Typography>\r\n            <Typography sx={{ color: \"text.secondary\" }}>\r\n              Sorry, we couldn’t find the page you’re looking for. Perhaps\r\n              you’ve mistyped the URL? Be sure to check your spelling.\r\n            </Typography>\r\n\r\n            <PageNotFoundIllustration\r\n              sx={{ height: 260, my: { xs: 5, sm: 10 } }}\r\n            />\r\n\r\n            <Button\r\n              size=\"large\"\r\n              variant=\"contained\"\r\n              onClick={() => {\r\n                navigate(-1);\r\n              }}\r\n            >\r\n              Go Back\r\n            </Button>\r\n          </Box>\r\n        </Container>\r\n      </RootStyle>\r\n    </>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C;AACA,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AAClE;AACA,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,SAASC,MAAM,QAAQ,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,SAAS,GAAGX,MAAM,CAACC,GAAG,CAAC,CAACW,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAC5CE,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAEJ,KAAK,CAACK,OAAO,CAAC,EAAE,CAAC;IAC7BC,aAAa,EAAEN,KAAK,CAACK,OAAO,CAAC,EAAE;EACjC,CAAC;AAAA,CAAC,CAAC;;AAEH;AAAAE,EAAA,GARMT,SAAS;AAUf,eAAe,SAASU,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,oBACES,OAAA,CAAAE,SAAA;IAAAc,QAAA,gBACEhB,OAAA,CAACF,MAAM;MAAAkB,QAAA,eACLhB,OAAA;QAAAgB,QAAA,EAAO;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACTpB,OAAA,CAACG,SAAS;MAAAa,QAAA,eACRhB,OAAA,CAACJ,SAAS;QAAAoB,QAAA,eACRhB,OAAA,CAACP,GAAG;UACF4B,EAAE,EAAE;YACFC,QAAQ,EAAE,GAAG;YACbC,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,gBAEFhB,OAAA,CAACL,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACC,SAAS;YAACC,KAAK,EAAC,SAAS;YAAAX,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpB,OAAA,CAACL,UAAU;YAAC0B,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAX,QAAA,EAAC;UAG7C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbpB,OAAA,CAACH,wBAAwB;YACvBwB,EAAE,EAAE;cAAEO,MAAM,EAAE,GAAG;cAAEC,EAAE,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAG;YAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEFpB,OAAA,CAACN,MAAM;YACLsC,IAAI,EAAC,OAAO;YACZP,OAAO,EAAC,WAAW;YACnBQ,OAAO,EAAEA,CAAA,KAAM;cACblB,QAAQ,CAAC,CAAC,CAAC,CAAC;YACd,CAAE;YAAAC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACZ,CAAC;AAEP;AAACN,EAAA,CA1CuBD,OAAO;EAAA,QACZtB,WAAW;AAAA;AAAA2C,GAAA,GADNrB,OAAO;AAAA,IAAAD,EAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}